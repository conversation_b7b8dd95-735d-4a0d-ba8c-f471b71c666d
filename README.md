# نظام إدارة معلومات الموظفين

## وصف المشروع
نظام شامل لإدارة معلومات الموظفين مطور بلغة C# باستخدام Windows Forms. يدعم النظام إدارة 46 عمود من البيانات الشخصية والمهنية لأكثر من 2000 موظف.

## الميزات الرئيسية

### 📊 إدارة البيانات
- **استيراد من Excel**: استيراد البيانات من ملفات Excel (.xlsx, .xls)
- **قاعدة بيانات SQLite**: تخزين آمن وسريع للبيانات
- **عرض البيانات**: جدول تفاعلي مع إمكانية التمرير والفرز

### 🔍 البحث والاستعلام
- **البحث السريع**: بحث فوري في الاسم، رقم التعريف الوطني، الرتبة، الولاية، ومكان العمل
- **البحث المتقدم**: فلترة متقدمة حسب:
  - الاسم ورقم التعريف الوطني
  - الرتبة والولاية ومكان العمل
  - الحالة العائلية
  - نطاق تاريخ الميلاد
  - نطاق تاريخ التوظيف
  - نطاق العمر

### 📈 الحسابات التلقائية
- **العمر**: حساب تلقائي من تاريخ الميلاد
- **أقدمية المهنة**: حساب من تاريخ التوظيف
- **أقدمية الرتبة**: حساب من تاريخ التعيين في الرتبة

### 🖨️ الطباعة والتصدير
- **طباعة البطاقات**: بطاقة معلومات شخصية مع QR Code
- **تصدير Excel**: تصدير البيانات الكاملة أو المفلترة
- **معاينة الطباعة**: مراجعة قبل الطباعة

## متطلبات النظام
- Windows 10 أو أحدث
- .NET 8.0 Runtime
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة تخزين

## التثبيت والتشغيل

### 1. تحميل المتطلبات
```bash
# تثبيت .NET 8.0 SDK
winget install Microsoft.DotNet.SDK.8

# أو تحميل من الموقع الرسمي
# https://dotnet.microsoft.com/download/dotnet/8.0
```

### 2. بناء المشروع
```bash
# استنساخ المشروع
git clone [repository-url]
cd EmployeeManagementSystem

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build --configuration Release

# تشغيل التطبيق
dotnet run
```

### 3. التشغيل المباشر
```bash
# تشغيل من مجلد البناء
cd bin/Release/net8.0-windows
./EmployeeManagementSystem.exe
```

## دليل الاستخدام

### استيراد البيانات من Excel
1. انقر على "ملف" → "استيراد من Excel"
2. اختر ملف Excel الذي يحتوي على بيانات الموظفين
3. تأكد من أن الأعمدة مرتبة حسب التسلسل المطلوب:
   - العمود 1: الرقم
   - العمود 2: رقم القيد
   - العمود 3: الرتبة
   - ... إلخ (46 عمود)
4. انقر "فتح" لبدء الاستيراد

### البحث في البيانات
#### البحث السريع:
- اكتب في مربع البحث في شريط الأدوات
- اضغط Enter أو انقر "بحث"

#### البحث المتقدم:
1. انقر "بحث متقدم" في شريط الأدوات
2. املأ المعايير المطلوبة
3. فعّل الفلاتر الإضافية حسب الحاجة
4. انقر "بحث"

### طباعة بطاقة الموظف
1. اختر الموظف من الجدول
2. انقر "طباعة البطاقة" أو انقر نقرة مزدوجة على الصف
3. راجع المعاينة
4. انقر "طباعة"

### تصدير البيانات
1. انقر "ملف" → "تصدير إلى Excel"
2. اختر مكان الحفظ
3. سيتم تصدير البيانات المعروضة حالياً

## هيكل البيانات

### الأعمدة المدعومة (46 عمود):
1. **البيانات الشخصية**: الاسم، تاريخ الميلاد، مكان الميلاد، رقم التعريف الوطني
2. **البيانات العائلية**: الحالة العائلية، معلومات الزوجة، عدد الأولاد
3. **البيانات المهنية**: الرتبة، مكان العمل، تاريخ التوظيف، الوظيفة
4. **البيانات الإدارية**: أرقام الحسابات، الضمان الاجتماعي، التعاضدية
5. **البيانات التعليمية**: نوع الشهادة، التخصص، جهة الإصدار
6. **بيانات أخرى**: الخدمة الوطنية، التحويلات

## الدعم الفني

### المشاكل الشائعة
1. **خطأ في استيراد Excel**: تأكد من تطابق ترتيب الأعمدة
2. **بطء في البحث**: أعد فهرسة قاعدة البيانات
3. **مشاكل الطباعة**: تحقق من إعدادات الطابعة

### ملفات السجل
- `EmployeeDatabase.sqlite`: قاعدة البيانات الرئيسية
- `logs/`: مجلد سجلات الأخطاء (إن وجد)

## التطوير المستقبلي
- [ ] نسخة ويب
- [ ] تطبيق موبايل
- [ ] تقارير متقدمة
- [ ] نظام صلاحيات المستخدمين
- [ ] نسخ احتياطية تلقائية

## الترخيص
هذا المشروع مطور للاستخدام الداخلي. جميع الحقوق محفوظة.

## المطور
تم تطوير هذا النظام باستخدام:
- **اللغة**: C# 12.0
- **الإطار**: .NET 8.0
- **واجهة المستخدم**: Windows Forms
- **قاعدة البيانات**: SQLite
- **مكتبات إضافية**: 
  - ExcelDataReader (قراءة Excel)
  - ClosedXML (كتابة Excel)
  - QRCoder (إنشاء QR Code)
  - System.Data.SQLite (قاعدة البيانات)

---
**ملاحظة**: تأكد من عمل نسخة احتياطية من بياناتك قبل إجراء أي تحديثات على النظام.
