using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using EmployeeManagementSystem.Data;
using EmployeeManagementSystem.Models;
using EmployeeManagementSystem.Services;

namespace EmployeeManagementSystem
{
    public class SimpleMainForm : Form
    {
        private DatabaseManager databaseManager;
        private List<User> allUsers;
        private List<User> filteredUsers;

        // عناصر الواجهة
        private DataGridView dataGridView;
        private TextBox searchTextBox;
        private Button searchButton;
        private Button importButton;
        private Button printButton;
        private Button exportButton;
        private Label statusLabel;

        public SimpleMainForm()
        {
            InitializeForm();
            InitializeServices();
            LoadData();
        }

        private void InitializeForm()
        {
            // إعدادات النافذة
            this.Text = "نظام إدارة معلومات الموظفين";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;

            // إنشاء العناصر
            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // مربع البحث
            searchTextBox = new TextBox()
            {
                Width = 200,
                Font = new Font("Arial", 10)
            };
            searchTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) PerformSearch(); };

            // أزرار
            searchButton = new Button()
            {
                Text = "بحث",
                Width = 80,
                Height = 30
            };
            searchButton.Click += (s, e) => PerformSearch();

            importButton = new Button()
            {
                Text = "استيراد Excel",
                Width = 100,
                Height = 30
            };
            importButton.Click += ImportButton_Click;

            printButton = new Button()
            {
                Text = "طباعة البطاقة",
                Width = 100,
                Height = 30
            };
            printButton.Click += PrintButton_Click;

            exportButton = new Button()
            {
                Text = "تصدير Excel",
                Width = 100,
                Height = 30
            };
            exportButton.Click += ExportButton_Click;

            // جدول البيانات
            dataGridView = new DataGridView()
            {
                Dock = DockStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RightToLeft = RightToLeft.Yes,
                Font = new Font("Arial", 9)
            };

            // تسمية الحالة
            statusLabel = new Label()
            {
                Text = "جاهز",
                AutoSize = true,
                Font = new Font("Arial", 9)
            };
        }

        private void LayoutControls()
        {
            // تخطيط العناصر
            int margin = 10;
            int currentY = margin;

            // الصف الأول - البحث
            var searchLabel = new Label()
            {
                Text = "البحث:",
                Location = new Point(margin, currentY + 5),
                AutoSize = true
            };
            this.Controls.Add(searchLabel);

            searchTextBox.Location = new Point(searchLabel.Right + 10, currentY);
            this.Controls.Add(searchTextBox);

            searchButton.Location = new Point(searchTextBox.Right + 10, currentY);
            this.Controls.Add(searchButton);

            currentY += 40;

            // الصف الثاني - الأزرار
            importButton.Location = new Point(margin, currentY);
            this.Controls.Add(importButton);

            printButton.Location = new Point(importButton.Right + 10, currentY);
            this.Controls.Add(printButton);

            exportButton.Location = new Point(printButton.Right + 10, currentY);
            this.Controls.Add(exportButton);

            currentY += 40;

            // الجدول
            dataGridView.Location = new Point(margin, currentY);
            dataGridView.Size = new Size(this.ClientSize.Width - 2 * margin, 
                                        this.ClientSize.Height - currentY - 40);
            this.Controls.Add(dataGridView);

            // شريط الحالة
            statusLabel.Location = new Point(margin, this.ClientSize.Height - 25);
            this.Controls.Add(statusLabel);
        }

        private void InitializeServices()
        {
            try
            {
                databaseManager = new DatabaseManager();
                allUsers = new List<User>();
                filteredUsers = new List<User>();
                statusLabel.Text = "تم تهيئة الخدمات بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الخدمات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في التهيئة";
            }
        }

        private void LoadData()
        {
            try
            {
                statusLabel.Text = "جاري تحميل البيانات...";
                Application.DoEvents();

                allUsers = databaseManager.GetAllUsers();
                filteredUsers = new List<User>(allUsers);

                UpdateDataGridView();
                statusLabel.Text = $"تم تحميل {allUsers.Count} سجل";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في تحميل البيانات";
            }
        }

        private void UpdateDataGridView()
        {
            try
            {
                var displayData = filteredUsers.Select(u => new
                {
                    الرقم = u.Id,
                    الاسم_الكامل = u.FullNameArabic ?? "",
                    الرتبة = u.Rank ?? "",
                    رقم_التعريف = u.NationalId ?? "",
                    العمر = u.Age,
                    الولاية = u.Province ?? "",
                    مكان_العمل = u.WorkPlace ?? "",
                    الهاتف = u.PersonalPhone ?? ""
                }).ToList();

                dataGridView.DataSource = displayData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PerformSearch()
        {
            try
            {
                string searchTerm = searchTextBox.Text.Trim();

                if (string.IsNullOrEmpty(searchTerm))
                {
                    filteredUsers = new List<User>(allUsers);
                }
                else
                {
                    filteredUsers = allUsers.Where(u =>
                        (u.FullNameArabic?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true) ||
                        (u.NationalId?.Contains(searchTerm) == true) ||
                        (u.Rank?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true) ||
                        (u.Province?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)
                    ).ToList();
                }

                UpdateDataGridView();
                statusLabel.Text = $"تم العثور على {filteredUsers.Count} نتيجة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ImportButton_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "Excel Files|*.xlsx;*.xls";
                openFileDialog.Title = "اختر ملف Excel";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    statusLabel.Text = "جاري استيراد البيانات...";
                    Application.DoEvents();

                    var excelImportService = new ExcelImportService(databaseManager);
                    var result = excelImportService.ImportFromExcel(openFileDialog.FileName);

                    string message = $"تم استيراد {result.SuccessCount} سجل بنجاح";
                    if (result.HasErrors)
                    {
                        message += $"\nفشل في استيراد {result.ErrorCount} سجل";
                    }

                    MessageBox.Show(message, "نتيجة الاستيراد", MessageBoxButtons.OK, 
                        result.HasErrors ? MessageBoxIcon.Warning : MessageBoxIcon.Information);

                    LoadData(); // إعادة تحميل البيانات
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد الملف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في الاستيراد";
            }
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار موظف لطباعة بطاقته", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int selectedId = (int)dataGridView.SelectedRows[0].Cells["الرقم"].Value;
                var selectedUser = allUsers.FirstOrDefault(u => u.Id == selectedId);

                if (selectedUser != null)
                {
                    var cardPrintService = new CardPrintService();
                    cardPrintService.PrintEmployeeCard(selectedUser);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportButton_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel Files|*.xlsx";
                saveFileDialog.Title = "حفظ ملف Excel";
                saveFileDialog.FileName = $"بيانات_الموظفين_{DateTime.Now:yyyy-MM-dd}";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    statusLabel.Text = "جاري تصدير البيانات...";
                    Application.DoEvents();

                    var exportService = new ExportService();
                    exportService.ExportToExcel(filteredUsers, saveFileDialog.FileName);

                    MessageBox.Show($"تم تصدير {filteredUsers.Count} سجل بنجاح", 
                        "تم التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    statusLabel.Text = "تم التصدير بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في التصدير";
            }
        }
    }
}
