# دليل المطور - نظام إدارة معلومات الموظفين

## هيكل المشروع

```
EmployeeManagementSystem/
├── Models/
│   └── User.cs                 # نموذج بيانات الموظف
├── Data/
│   └── DatabaseManager.cs     # إدارة قاعدة البيانات
├── Services/
│   ├── ExcelImportService.cs  # خدمة استيراد Excel
│   ├── CardPrintService.cs    # خدمة طباعة البطاقات
│   └── ExportService.cs       # خدمة التصدير
├── Forms/
│   ├── MainForm.cs            # النموذج الرئيسي
│   └── AdvancedSearchForm.cs  # نموذج البحث المتقدم
├── Program.cs                 # نقطة دخول التطبيق
├── EmployeeManagementSystem.csproj
└── README.md
```

## المكونات الرئيسية

### 1. نموذج البيانات (User.cs)
```csharp
public class User
{
    // 46 خاصية للبيانات الأساسية
    public string? FullNameArabic { get; set; }
    public DateTime? DateOfBirth { get; set; }
    // ...

    // خصائص محسوبة
    public int Age { get; }
    public string RankSeniority { get; }
    public string JobSeniority { get; }
}
```

**الميزات**:
- حساب تلقائي للعمر والأقدمية
- دالة توليد بيانات QR Code
- دعم القيم الفارغة (Nullable)

### 2. إدارة قاعدة البيانات (DatabaseManager.cs)
```csharp
public class DatabaseManager
{
    public void InsertUser(User user)
    public List<User> GetAllUsers()
    public List<User> SearchUsers(string searchTerm)
    public DataTable GetUsersDataTable()
}
```

**الميزات**:
- إنشاء تلقائي لقاعدة البيانات والجداول
- فهرسة للبحث السريع
- معالجة آمنة للاستعلامات (Parameterized Queries)

### 3. خدمة استيراد Excel (ExcelImportService.cs)
```csharp
public class ExcelImportService
{
    public ImportResult ImportFromExcel(string filePath)
}

public class ImportResult
{
    public int SuccessCount { get; set; }
    public int ErrorCount { get; set; }
    public List<string> ErrorMessages { get; set; }
}
```

**الميزات**:
- دعم تنسيقات Excel المختلفة
- تقرير مفصل عن نتائج الاستيراد
- معالجة الأخطاء والاستثناءات

### 4. خدمة طباعة البطاقات (CardPrintService.cs)
```csharp
public class CardPrintService
{
    public void PrintEmployeeCard(User user)
    public void DirectPrint(User user)
    private Bitmap GenerateQRCode(string data)
}
```

**الميزات**:
- تصميم بطاقة احترافية
- QR Code مع معلومات الموظف
- معاينة قبل الطباعة

### 5. خدمة التصدير (ExportService.cs)
```csharp
public class ExportService
{
    public void ExportToExcel(List<User> users, string filePath)
    public void ExportFilteredData(List<User> users, string filePath, string filterDescription)
}
```

**الميزات**:
- تصدير البيانات الكاملة أو المفلترة
- تنسيق احترافي للجداول
- دعم اللغة العربية (RTL)

## إضافة ميزات جديدة

### إضافة عمود جديد
1. **تحديث نموذج User**:
```csharp
public string? NewField { get; set; }
```

2. **تحديث قاعدة البيانات**:
```csharp
// في DatabaseManager.cs
string createTableQuery = @"
    CREATE TABLE IF NOT EXISTS Users (
        ...
        NewField TEXT,
        ...
    );";
```

3. **تحديث خدمة الاستيراد**:
```csharp
// في ExcelImportService.cs
user.NewField = GetCellValue(row, columnIndex)?.ToString();
```

4. **تحديث خدمة التصدير**:
```csharp
// في ExportService.cs
worksheet.Cell(row, columnIndex).Value = user.NewField ?? "";
```

### إضافة فلتر بحث جديد
1. **تحديث AdvancedSearchForm**:
```csharp
private ComboBox newFilterComboBox;

// في PopulateComboBoxes()
var newFilterValues = allUsers.Select(u => u.NewField).Distinct().ToList();
newFilterComboBox.Items.AddRange(newFilterValues.ToArray());

// في PerformSearch()
if (newFilterComboBox.SelectedIndex > 0)
{
    results = results.Where(u => u.NewField == newFilterComboBox.SelectedItem.ToString());
}
```

### إضافة تقرير جديد
1. **إنشاء فئة التقرير**:
```csharp
public class ReportService
{
    public void GenerateAgeReport(List<User> users)
    public void GenerateSeniorityReport(List<User> users)
}
```

2. **إضافة قائمة التقارير**:
```csharp
// في MainForm.cs
var reportsMenu = new ToolStripMenuItem("تقارير");
reportsMenu.DropDownItems.Add("تقرير الأعمار", null, AgeReport_Click);
```

## أفضل الممارسات

### 1. معالجة الأخطاء
```csharp
try
{
    // العملية الرئيسية
}
catch (Exception ex)
{
    MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
        MessageBoxButtons.OK, MessageBoxIcon.Error);
    // تسجيل الخطأ
}
```

### 2. إدارة الموارد
```csharp
using (var connection = new SQLiteConnection(connectionString))
{
    // استخدام الاتصال
} // يتم إغلاق الاتصال تلقائياً
```

### 3. التحقق من صحة البيانات
```csharp
if (string.IsNullOrEmpty(user.NationalId))
{
    throw new ArgumentException("رقم التعريف الوطني مطلوب");
}
```

### 4. الأداء
```csharp
// استخدام الفهارس للبحث السريع
"CREATE INDEX IF NOT EXISTS idx_national_id ON Users(NationalId);"

// تحميل البيانات بشكل غير متزامن
private async Task LoadDataAsync()
{
    await Task.Run(() => {
        // تحميل البيانات
    });
}
```

## اختبار التطبيق

### 1. اختبار الوحدة
```csharp
[Test]
public void CalculateAge_ValidDate_ReturnsCorrectAge()
{
    var user = new User { DateOfBirth = new DateTime(1990, 1, 1) };
    Assert.AreEqual(34, user.Age); // حسب السنة الحالية
}
```

### 2. اختبار التكامل
```csharp
[Test]
public void DatabaseManager_InsertAndRetrieve_Success()
{
    var dbManager = new DatabaseManager(":memory:");
    var user = new User { FullNameArabic = "اختبار" };
    
    dbManager.InsertUser(user);
    var users = dbManager.GetAllUsers();
    
    Assert.AreEqual(1, users.Count);
    Assert.AreEqual("اختبار", users[0].FullNameArabic);
}
```

## نشر التطبيق

### 1. بناء للنشر
```bash
dotnet publish -c Release -r win-x64 --self-contained true
```

### 2. إنشاء مثبت
```bash
# استخدام WiX Toolset أو Inno Setup
# أو إنشاء ملف ZIP بسيط
```

### 3. متطلبات النشر
- تضمين ملفات .NET Runtime
- تضمين قاعدة البيانات الفارغة
- ملفات التكوين الافتراضية

## الصيانة والتحديث

### 1. نسخ احتياطية
```csharp
public void BackupDatabase(string backupPath)
{
    File.Copy("EmployeeDatabase.sqlite", backupPath, true);
}
```

### 2. تحديث قاعدة البيانات
```csharp
public void UpdateDatabaseSchema()
{
    // فحص إصدار قاعدة البيانات
    // تطبيق التحديثات المطلوبة
}
```

### 3. سجلات النظام
```csharp
public static class Logger
{
    public static void LogError(string message, Exception ex)
    {
        File.AppendAllText("error.log", 
            $"{DateTime.Now}: {message} - {ex.Message}\n");
    }
}
```

## الأمان

### 1. حماية البيانات
- تشفير قاعدة البيانات الحساسة
- التحقق من صحة المدخلات
- منع SQL Injection

### 2. صلاحيات المستخدمين
```csharp
public enum UserRole
{
    Viewer,
    Editor,
    Admin
}

public class UserPermissions
{
    public bool CanView { get; set; }
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
}
```

## المساهمة في المشروع

### 1. معايير الكود
- استخدام أسماء واضحة للمتغيرات والدوال
- إضافة تعليقات للكود المعقد
- اتباع معايير C# الرسمية

### 2. اختبار التغييرات
- اختبار جميع الوظائف المتأثرة
- التأكد من عدم كسر الوظائف الموجودة
- اختبار الأداء مع بيانات كبيرة

### 3. توثيق التغييرات
- تحديث README.md
- إضافة تعليقات XML للدوال الجديدة
- توثيق أي تغييرات في قاعدة البيانات
