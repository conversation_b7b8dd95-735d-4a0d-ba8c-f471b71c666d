@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    نظام إدارة معلومات الموظفين
echo ========================================
echo.
echo جاري تشغيل التطبيق...
echo.

REM تحديث متغيرات البيئة
for /f "tokens=2*" %%a in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SystemPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SystemPath%;%UserPath%"

REM تشغيل التطبيق
dotnet run --project EmployeeManagementSystem.csproj --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ في تشغيل التطبيق
    echo يرجى التأكد من تثبيت .NET 8.0 أو أحدث
    echo.
    pause
)