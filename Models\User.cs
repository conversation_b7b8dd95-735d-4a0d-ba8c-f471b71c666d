using System;
using System.ComponentModel.DataAnnotations;

namespace EmployeeManagementSystem.Models
{
    public class User
    {
        // الأعمدة الأساسية
        public int Id { get; set; }
        public string? RegistrationNumber { get; set; } // رقم القيد
        public string? Rank { get; set; } // الرتبة
        public string? FullNameArabic { get; set; } // الاسم واللقب
        public string? LastName { get; set; } // NOM
        public string? FirstName { get; set; } // PRENOM
        public DateTime? DateOfBirth { get; set; } // تاريخ الميلاد
        public string? PlaceOfBirth { get; set; } // مكان الميلاد البلدية بالضبط
        public string? Province { get; set; } // الولاية
        public string? BirthCertificateNumber { get; set; } // رقم عقد الميلاد
        public string? NationalId { get; set; } // رقم التعريف الوطني NIN
        public string? BloodType { get; set; } // الزمرة الدموية
        public string? FatherName { get; set; } // إسم الأب
        public string? MotherName { get; set; } // اسم ولقب الأم
        public string? MaritalStatus { get; set; } // الحالة العائلية
        public int? NumberOfChildren { get; set; } // عدد الأولاد
        public string? SpouseName { get; set; } // اسم ولقب الزوجة
        public DateTime? SpouseDateOfBirth { get; set; } // تاريخ ميلاد الزوجة
        public string? SpousePlaceOfBirth { get; set; } // مكان ميلاد الزوجة
        public DateTime? MarriageDate { get; set; } // تاريخ الزواج
        public string? SpouseOccupation { get; set; } // مهنة الزوجة
        public string? Address { get; set; } // العنوان
        public string? PersonalPhone { get; set; } // رقم الهاتف الشخصي
        public string? Email { get; set; } // البريد الالكتروني
        public string? WorkPlace { get; set; } // مكان العمل
        public DateTime? EmploymentDate { get; set; } // تاريخ التوظيف
        public DateTime? RankAppointmentDate { get; set; } // تاريخ التعيين في الرتبة
        public string? CurrentGrade { get; set; } // الدرجة الحالية
        public DateTime? EffectiveDate { get; set; } // تاريخ السريان
        public string? WorkSystem { get; set; } // نظام العمل
        public string? Position { get; set; } // الوظيفة
        public string? PostalAccountNumber { get; set; } // رقم الحساب البريدي
        public string? SocialSecurityNumber { get; set; } // رقم الضمان الاجتماعي
        public string? ProfessionalIdNumber { get; set; } // رقم بطاقة التعريف المهنية
        public DateTime? IdCardIssueDate { get; set; } // تاريخ الصدور البطاقة
        public string? MutualInsuranceNumber { get; set; } // رقم الإنخراط في التعاضدية
        public string? CertificateType { get; set; } // نوع الشهادة
        public string? Specialization { get; set; } // التخصص
        public string? CertificateNumber { get; set; } // رقم الشهادة
        public DateTime? CertificateIssueDate { get; set; } // تاريخ الصدور
        public string? IssuingInstitution { get; set; } // جهة الاصدار
        public string? BeforeOrAfterEmployment { get; set; } // قبل او بعد التوظيف
        public string? MilitaryServiceStatus { get; set; } // الوضعية اتجاه الخدمة الوطنية
        public DateTime? ValidityDate { get; set; } // تاريخ الصلاحية الى غاية
        public DateTime? InternalTransferDate { get; set; } // تاريخ التحويل الداخلي
        public DateTime? ExternalTransferDate { get; set; } // تاريخ التحويل من خارج الولاية

        // الخصائص المحسوبة
        public int Age
        {
            get
            {
                if (!DateOfBirth.HasValue) return 0;
                return CalculateAge(DateOfBirth.Value);
            }
        }

        public string RankSeniority
        {
            get
            {
                if (!RankAppointmentDate.HasValue) return "غير محدد";
                return CalculateSeniority(RankAppointmentDate.Value);
            }
        }

        public string JobSeniority
        {
            get
            {
                if (!EmploymentDate.HasValue) return "غير محدد";
                return CalculateSeniority(EmploymentDate.Value);
            }
        }

        // دوال مساعدة
        private int CalculateAge(DateTime dateOfBirth)
        {
            DateTime today = DateTime.Today;
            int age = today.Year - dateOfBirth.Year;
            if (dateOfBirth.Date > today.AddYears(-age)) age--;
            return age;
        }

        private string CalculateSeniority(DateTime startDate)
        {
            DateTime today = DateTime.Today;
            int years = today.Year - startDate.Year;
            int months = today.Month - startDate.Month;
            int days = today.Day - startDate.Day;

            if (days < 0)
            {
                months--;
                days += DateTime.DaysInMonth(today.Year, today.Month == 1 ? 12 : today.Month - 1);
            }

            if (months < 0)
            {
                years--;
                months += 12;
            }

            if (years > 0 && months > 0)
                return $"{years} سنة و {months} شهر";
            else if (years > 0)
                return $"{years} سنة";
            else if (months > 0)
                return $"{months} شهر";
            else
                return "أقل من شهر";
        }

        // دالة لتوليد بيانات QR Code
        public string GetQRData()
        {
            return $"ID:{NationalId}|Name:{FullNameArabic}|Rank:{Rank}|Workplace:{WorkPlace}";
        }
    }
}
