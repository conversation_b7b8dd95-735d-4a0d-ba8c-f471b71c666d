@echo off
chcp 65001 >nul
echo.
echo ========================================
echo  🎨 تشغيل النسخة العربية - خط Cairo
echo ========================================
echo.

echo ✨ المميزات العربية الجديدة:
echo • 🎨 خط Cairo الأنيق المُصمم للعربية
echo • 🔄 واجهة عربية كاملة RTL
echo • 📝 محاذاة يمين لجميع النصوص
echo • 🎯 ترتيب عربي للقوائم والأزرار
echo • 📊 جداول عربية التوجه
echo • 🔍 بحث عربي من اليمين لليسار
echo.

echo 🚀 جاري التشغيل...
echo.

REM تحديث متغيرات البيئة
for /f "tokens=2*" %%a in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SystemPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SystemPath%;%UserPath%"

REM تشغيل النسخة الاحترافية العربية
if exist "bin\Release\net8.0-windows\EmployeeManagementSystem.exe" (
    echo ✅ تشغيل من الملف الجاهز...
    start "نظام إدارة الموظفين العربي" "bin\Release\net8.0-windows\EmployeeManagementSystem.exe"
) else (
    echo 🔨 بناء النسخة أولاً...
    dotnet run --project EmployeeManagementSystem.csproj --configuration Release
)

echo.
echo 🎊 مرحباً بك في النسخة العربية!
timeout /t 3 >nul