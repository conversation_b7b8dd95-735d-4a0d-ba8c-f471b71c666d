using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using ExcelDataReader;
using EmployeeManagementSystem.Models;
using EmployeeManagementSystem.Data;

namespace EmployeeManagementSystem.Services
{
    public class ExcelImportService
    {
        private readonly DatabaseManager databaseManager;

        public ExcelImportService(DatabaseManager dbManager)
        {
            databaseManager = dbManager;
            // تسجيل مزود الترميز للعمل مع ملفات Excel
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
        }

        public ImportResult ImportFromExcel(string filePath)
        {
            var result = new ImportResult();
            
            try
            {
                using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read))
                {
                    using (var reader = ExcelReaderFactory.CreateReader(stream))
                    {
                        var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration()
                        {
                            ConfigureDataTable = (_) => new ExcelDataTableConfiguration()
                            {
                                UseHeaderRow = true
                            }
                        });

                        var dataTable = dataSet.Tables[0];
                        
                        for (int i = 0; i < dataTable.Rows.Count; i++)
                        {
                            try
                            {
                                var row = dataTable.Rows[i];
                                var user = MapRowToUser(row, dataTable.Columns);
                                
                                if (!string.IsNullOrEmpty(user.NationalId))
                                {
                                    databaseManager.InsertUser(user);
                                    result.SuccessCount++;
                                }
                                else
                                {
                                    result.ErrorMessages.Add($"الصف {i + 2}: رقم التعريف الوطني مفقود");
                                    result.ErrorCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                result.ErrorMessages.Add($"الصف {i + 2}: {ex.Message}");
                                result.ErrorCount++;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.ErrorMessages.Add($"خطأ في قراءة الملف: {ex.Message}");
                result.ErrorCount++;
            }

            return result;
        }

        private User MapRowToUser(DataRow row, DataColumnCollection columns)
        {
            var user = new User();

            // تعيين القيم حسب ترتيب الأعمدة المذكورة
            user.RegistrationNumber = GetCellValue(row, 1)?.ToString(); // رقم القيد
            user.Rank = GetCellValue(row, 2)?.ToString(); // الرتبة
            user.FullNameArabic = GetCellValue(row, 3)?.ToString(); // الاسم واللقب
            user.LastName = GetCellValue(row, 4)?.ToString(); // NOM
            user.FirstName = GetCellValue(row, 5)?.ToString(); // PRENOM
            user.DateOfBirth = ParseDate(GetCellValue(row, 6)); // تاريخ الميلاد
            user.PlaceOfBirth = GetCellValue(row, 7)?.ToString(); // مكان الميلاد
            user.Province = GetCellValue(row, 8)?.ToString(); // الولاية
            user.BirthCertificateNumber = GetCellValue(row, 9)?.ToString(); // رقم عقد الميلاد
            user.NationalId = GetCellValue(row, 10)?.ToString(); // رقم التعريف الوطني
            user.BloodType = GetCellValue(row, 11)?.ToString(); // الزمرة الدموية
            user.FatherName = GetCellValue(row, 12)?.ToString(); // إسم الأب
            user.MotherName = GetCellValue(row, 13)?.ToString(); // اسم ولقب الأم
            user.MaritalStatus = GetCellValue(row, 14)?.ToString(); // الحالة العائلية
            user.NumberOfChildren = ParseInt(GetCellValue(row, 15)); // عدد الأولاد
            user.SpouseName = GetCellValue(row, 16)?.ToString(); // اسم ولقب الزوجة
            user.SpouseDateOfBirth = ParseDate(GetCellValue(row, 17)); // تاريخ ميلاد الزوجة
            user.SpousePlaceOfBirth = GetCellValue(row, 18)?.ToString(); // مكان ميلاد الزوجة
            user.MarriageDate = ParseDate(GetCellValue(row, 19)); // تاريخ الزواج
            user.SpouseOccupation = GetCellValue(row, 20)?.ToString(); // مهنة الزوجة
            user.Address = GetCellValue(row, 21)?.ToString(); // العنوان
            user.PersonalPhone = GetCellValue(row, 22)?.ToString(); // رقم الهاتف الشخصي
            user.Email = GetCellValue(row, 23)?.ToString(); // البريد الالكتروني
            user.WorkPlace = GetCellValue(row, 24)?.ToString(); // مكان العمل
            user.EmploymentDate = ParseDate(GetCellValue(row, 25)); // تاريخ التوظيف
            user.RankAppointmentDate = ParseDate(GetCellValue(row, 26)); // تاريخ التعيين في الرتبة
            user.CurrentGrade = GetCellValue(row, 27)?.ToString(); // الدرجة الحالية
            user.EffectiveDate = ParseDate(GetCellValue(row, 28)); // تاريخ السريان
            user.WorkSystem = GetCellValue(row, 29)?.ToString(); // نظام العمل
            user.Position = GetCellValue(row, 30)?.ToString(); // الوظيفة
            user.PostalAccountNumber = GetCellValue(row, 31)?.ToString(); // رقم الحساب البريدي
            user.SocialSecurityNumber = GetCellValue(row, 32)?.ToString(); // رقم الضمان الاجتماعي
            user.ProfessionalIdNumber = GetCellValue(row, 33)?.ToString(); // رقم بطاقة التعريف المهنية
            user.IdCardIssueDate = ParseDate(GetCellValue(row, 34)); // تاريخ الصدور البطاقة
            user.MutualInsuranceNumber = GetCellValue(row, 35)?.ToString(); // رقم الإنخراط في التعاضدية
            user.CertificateType = GetCellValue(row, 36)?.ToString(); // نوع الشهادة
            user.Specialization = GetCellValue(row, 37)?.ToString(); // التخصص
            user.CertificateNumber = GetCellValue(row, 38)?.ToString(); // رقم الشهادة
            user.CertificateIssueDate = ParseDate(GetCellValue(row, 39)); // تاريخ الصدور
            user.IssuingInstitution = GetCellValue(row, 40)?.ToString(); // جهة الاصدار
            user.BeforeOrAfterEmployment = GetCellValue(row, 41)?.ToString(); // قبل او بعد التوظيف
            user.MilitaryServiceStatus = GetCellValue(row, 42)?.ToString(); // الوضعية اتجاه الخدمة الوطنية
            user.ValidityDate = ParseDate(GetCellValue(row, 43)); // تاريخ الصلاحية
            user.InternalTransferDate = ParseDate(GetCellValue(row, 44)); // تاريخ التحويل الداخلي
            user.ExternalTransferDate = ParseDate(GetCellValue(row, 45)); // تاريخ التحويل من خارج الولاية

            return user;
        }

        private object? GetCellValue(DataRow row, int columnIndex)
        {
            if (columnIndex < row.Table.Columns.Count && row[columnIndex] != DBNull.Value)
            {
                return row[columnIndex];
            }
            return null;
        }

        private DateTime? ParseDate(object? value)
        {
            if (value == null) return null;

            if (value is DateTime dateTime)
                return dateTime;

            if (DateTime.TryParse(value.ToString(), out DateTime result))
                return result;

            return null;
        }

        private int? ParseInt(object? value)
        {
            if (value == null) return null;

            if (value is int intValue)
                return intValue;

            if (int.TryParse(value.ToString(), out int result))
                return result;

            return null;
        }
    }

    public class ImportResult
    {
        public int SuccessCount { get; set; } = 0;
        public int ErrorCount { get; set; } = 0;
        public List<string> ErrorMessages { get; set; } = new List<string>();

        public bool HasErrors => ErrorCount > 0;
        public int TotalProcessed => SuccessCount + ErrorCount;
    }
}
