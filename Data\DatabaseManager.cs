using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using EmployeeManagementSystem.Models;

namespace EmployeeManagementSystem.Data
{
    public class DatabaseManager
    {
        private readonly string connectionString;

        public DatabaseManager(string databasePath = "EmployeeDatabase.sqlite")
        {
            connectionString = $"Data Source={databasePath};Version=3;";
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                string createTableQuery = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RegistrationNumber TEXT,
                    Rank TEXT,
                    FullNameArabic TEXT,
                    LastName TEXT,
                    FirstName TEXT,
                    DateOfBirth TEXT,
                    PlaceOfBirth TEXT,
                    Province TEXT,
                    BirthCertificateNumber TEXT,
                    NationalId TEXT UNIQUE,
                    BloodType TEXT,
                    FatherName TEXT,
                    MotherName TEXT,
                    MaritalStatus TEXT,
                    NumberOfChildren INTEGER,
                    SpouseName TEXT,
                    SpouseDateOfBirth TEXT,
                    SpousePlaceOfBirth TEXT,
                    MarriageDate TEXT,
                    SpouseOccupation TEXT,
                    Address TEXT,
                    PersonalPhone TEXT,
                    Email TEXT,
                    WorkPlace TEXT,
                    EmploymentDate TEXT,
                    RankAppointmentDate TEXT,
                    CurrentGrade TEXT,
                    EffectiveDate TEXT,
                    WorkSystem TEXT,
                    Position TEXT,
                    PostalAccountNumber TEXT,
                    SocialSecurityNumber TEXT,
                    ProfessionalIdNumber TEXT,
                    IdCardIssueDate TEXT,
                    MutualInsuranceNumber TEXT,
                    CertificateType TEXT,
                    Specialization TEXT,
                    CertificateNumber TEXT,
                    CertificateIssueDate TEXT,
                    IssuingInstitution TEXT,
                    BeforeOrAfterEmployment TEXT,
                    MilitaryServiceStatus TEXT,
                    ValidityDate TEXT,
                    InternalTransferDate TEXT,
                    ExternalTransferDate TEXT
                );";

                using (var command = new SQLiteCommand(createTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                // إنشاء فهارس للبحث السريع
                string[] indexQueries = {
                    "CREATE INDEX IF NOT EXISTS idx_national_id ON Users(NationalId);",
                    "CREATE INDEX IF NOT EXISTS idx_full_name ON Users(FullNameArabic);",
                    "CREATE INDEX IF NOT EXISTS idx_rank ON Users(Rank);",
                    "CREATE INDEX IF NOT EXISTS idx_province ON Users(Province);",
                    "CREATE INDEX IF NOT EXISTS idx_workplace ON Users(WorkPlace);"
                };

                foreach (string indexQuery in indexQueries)
                {
                    using (var command = new SQLiteCommand(indexQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
        }

        public void InsertUser(User user)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string insertQuery = @"
                INSERT INTO Users (
                    RegistrationNumber, Rank, FullNameArabic, LastName, FirstName,
                    DateOfBirth, PlaceOfBirth, Province, BirthCertificateNumber, NationalId,
                    BloodType, FatherName, MotherName, MaritalStatus, NumberOfChildren,
                    SpouseName, SpouseDateOfBirth, SpousePlaceOfBirth, MarriageDate, SpouseOccupation,
                    Address, PersonalPhone, Email, WorkPlace, EmploymentDate,
                    RankAppointmentDate, CurrentGrade, EffectiveDate, WorkSystem, Position,
                    PostalAccountNumber, SocialSecurityNumber, ProfessionalIdNumber, IdCardIssueDate, MutualInsuranceNumber,
                    CertificateType, Specialization, CertificateNumber, CertificateIssueDate, IssuingInstitution,
                    BeforeOrAfterEmployment, MilitaryServiceStatus, ValidityDate, InternalTransferDate, ExternalTransferDate
                ) VALUES (
                    @RegistrationNumber, @Rank, @FullNameArabic, @LastName, @FirstName,
                    @DateOfBirth, @PlaceOfBirth, @Province, @BirthCertificateNumber, @NationalId,
                    @BloodType, @FatherName, @MotherName, @MaritalStatus, @NumberOfChildren,
                    @SpouseName, @SpouseDateOfBirth, @SpousePlaceOfBirth, @MarriageDate, @SpouseOccupation,
                    @Address, @PersonalPhone, @Email, @WorkPlace, @EmploymentDate,
                    @RankAppointmentDate, @CurrentGrade, @EffectiveDate, @WorkSystem, @Position,
                    @PostalAccountNumber, @SocialSecurityNumber, @ProfessionalIdNumber, @IdCardIssueDate, @MutualInsuranceNumber,
                    @CertificateType, @Specialization, @CertificateNumber, @CertificateIssueDate, @IssuingInstitution,
                    @BeforeOrAfterEmployment, @MilitaryServiceStatus, @ValidityDate, @InternalTransferDate, @ExternalTransferDate
                );";

                using (var command = new SQLiteCommand(insertQuery, connection))
                {
                    AddUserParameters(command, user);
                    command.ExecuteNonQuery();
                }
            }
        }

        public List<User> GetAllUsers()
        {
            var users = new List<User>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string selectQuery = "SELECT * FROM Users ORDER BY FullNameArabic";
                
                using (var command = new SQLiteCommand(selectQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        users.Add(MapReaderToUser(reader));
                    }
                }
            }
            return users;
        }

        public List<User> SearchUsers(string searchTerm)
        {
            var users = new List<User>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string searchQuery = @"
                SELECT * FROM Users 
                WHERE FullNameArabic LIKE @search 
                   OR NationalId LIKE @search 
                   OR Rank LIKE @search 
                   OR Province LIKE @search 
                   OR WorkPlace LIKE @search
                ORDER BY FullNameArabic";
                
                using (var command = new SQLiteCommand(searchQuery, connection))
                {
                    command.Parameters.AddWithValue("@search", $"%{searchTerm}%");
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            users.Add(MapReaderToUser(reader));
                        }
                    }
                }
            }
            return users;
        }

        public DataTable GetUsersDataTable()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string selectQuery = "SELECT * FROM Users ORDER BY FullNameArabic";
                
                using (var adapter = new SQLiteDataAdapter(selectQuery, connection))
                {
                    var dataTable = new DataTable();
                    adapter.Fill(dataTable);
                    return dataTable;
                }
            }
        }

        private void AddUserParameters(SQLiteCommand command, User user)
        {
            command.Parameters.AddWithValue("@RegistrationNumber", user.RegistrationNumber ?? "");
            command.Parameters.AddWithValue("@Rank", user.Rank ?? "");
            command.Parameters.AddWithValue("@FullNameArabic", user.FullNameArabic ?? "");
            command.Parameters.AddWithValue("@LastName", user.LastName ?? "");
            command.Parameters.AddWithValue("@FirstName", user.FirstName ?? "");
            command.Parameters.AddWithValue("@DateOfBirth", user.DateOfBirth?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@PlaceOfBirth", user.PlaceOfBirth ?? "");
            command.Parameters.AddWithValue("@Province", user.Province ?? "");
            command.Parameters.AddWithValue("@BirthCertificateNumber", user.BirthCertificateNumber ?? "");
            command.Parameters.AddWithValue("@NationalId", user.NationalId ?? "");
            command.Parameters.AddWithValue("@BloodType", user.BloodType ?? "");
            command.Parameters.AddWithValue("@FatherName", user.FatherName ?? "");
            command.Parameters.AddWithValue("@MotherName", user.MotherName ?? "");
            command.Parameters.AddWithValue("@MaritalStatus", user.MaritalStatus ?? "");
            command.Parameters.AddWithValue("@NumberOfChildren", user.NumberOfChildren ?? 0);
            command.Parameters.AddWithValue("@SpouseName", user.SpouseName ?? "");
            command.Parameters.AddWithValue("@SpouseDateOfBirth", user.SpouseDateOfBirth?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@SpousePlaceOfBirth", user.SpousePlaceOfBirth ?? "");
            command.Parameters.AddWithValue("@MarriageDate", user.MarriageDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@SpouseOccupation", user.SpouseOccupation ?? "");
            command.Parameters.AddWithValue("@Address", user.Address ?? "");
            command.Parameters.AddWithValue("@PersonalPhone", user.PersonalPhone ?? "");
            command.Parameters.AddWithValue("@Email", user.Email ?? "");
            command.Parameters.AddWithValue("@WorkPlace", user.WorkPlace ?? "");
            command.Parameters.AddWithValue("@EmploymentDate", user.EmploymentDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@RankAppointmentDate", user.RankAppointmentDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@CurrentGrade", user.CurrentGrade ?? "");
            command.Parameters.AddWithValue("@EffectiveDate", user.EffectiveDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@WorkSystem", user.WorkSystem ?? "");
            command.Parameters.AddWithValue("@Position", user.Position ?? "");
            command.Parameters.AddWithValue("@PostalAccountNumber", user.PostalAccountNumber ?? "");
            command.Parameters.AddWithValue("@SocialSecurityNumber", user.SocialSecurityNumber ?? "");
            command.Parameters.AddWithValue("@ProfessionalIdNumber", user.ProfessionalIdNumber ?? "");
            command.Parameters.AddWithValue("@IdCardIssueDate", user.IdCardIssueDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@MutualInsuranceNumber", user.MutualInsuranceNumber ?? "");
            command.Parameters.AddWithValue("@CertificateType", user.CertificateType ?? "");
            command.Parameters.AddWithValue("@Specialization", user.Specialization ?? "");
            command.Parameters.AddWithValue("@CertificateNumber", user.CertificateNumber ?? "");
            command.Parameters.AddWithValue("@CertificateIssueDate", user.CertificateIssueDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@IssuingInstitution", user.IssuingInstitution ?? "");
            command.Parameters.AddWithValue("@BeforeOrAfterEmployment", user.BeforeOrAfterEmployment ?? "");
            command.Parameters.AddWithValue("@MilitaryServiceStatus", user.MilitaryServiceStatus ?? "");
            command.Parameters.AddWithValue("@ValidityDate", user.ValidityDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@InternalTransferDate", user.InternalTransferDate?.ToString("yyyy-MM-dd") ?? "");
            command.Parameters.AddWithValue("@ExternalTransferDate", user.ExternalTransferDate?.ToString("yyyy-MM-dd") ?? "");
        }

        private User MapReaderToUser(SQLiteDataReader reader)
        {
            return new User
            {
                Id = Convert.ToInt32(reader["Id"]),
                RegistrationNumber = reader["RegistrationNumber"].ToString(),
                Rank = reader["Rank"].ToString(),
                FullNameArabic = reader["FullNameArabic"].ToString(),
                LastName = reader["LastName"].ToString(),
                FirstName = reader["FirstName"].ToString(),
                DateOfBirth = ParseDateTime(reader["DateOfBirth"].ToString()),
                PlaceOfBirth = reader["PlaceOfBirth"].ToString(),
                Province = reader["Province"].ToString(),
                BirthCertificateNumber = reader["BirthCertificateNumber"].ToString(),
                NationalId = reader["NationalId"].ToString(),
                BloodType = reader["BloodType"].ToString(),
                FatherName = reader["FatherName"].ToString(),
                MotherName = reader["MotherName"].ToString(),
                MaritalStatus = reader["MaritalStatus"].ToString(),
                NumberOfChildren = ParseInt(reader["NumberOfChildren"].ToString()),
                SpouseName = reader["SpouseName"].ToString(),
                SpouseDateOfBirth = ParseDateTime(reader["SpouseDateOfBirth"].ToString()),
                SpousePlaceOfBirth = reader["SpousePlaceOfBirth"].ToString(),
                MarriageDate = ParseDateTime(reader["MarriageDate"].ToString()),
                SpouseOccupation = reader["SpouseOccupation"].ToString(),
                Address = reader["Address"].ToString(),
                PersonalPhone = reader["PersonalPhone"].ToString(),
                Email = reader["Email"].ToString(),
                WorkPlace = reader["WorkPlace"].ToString(),
                EmploymentDate = ParseDateTime(reader["EmploymentDate"].ToString()),
                RankAppointmentDate = ParseDateTime(reader["RankAppointmentDate"].ToString()),
                CurrentGrade = reader["CurrentGrade"].ToString(),
                EffectiveDate = ParseDateTime(reader["EffectiveDate"].ToString()),
                WorkSystem = reader["WorkSystem"].ToString(),
                Position = reader["Position"].ToString(),
                PostalAccountNumber = reader["PostalAccountNumber"].ToString(),
                SocialSecurityNumber = reader["SocialSecurityNumber"].ToString(),
                ProfessionalIdNumber = reader["ProfessionalIdNumber"].ToString(),
                IdCardIssueDate = ParseDateTime(reader["IdCardIssueDate"].ToString()),
                MutualInsuranceNumber = reader["MutualInsuranceNumber"].ToString(),
                CertificateType = reader["CertificateType"].ToString(),
                Specialization = reader["Specialization"].ToString(),
                CertificateNumber = reader["CertificateNumber"].ToString(),
                CertificateIssueDate = ParseDateTime(reader["CertificateIssueDate"].ToString()),
                IssuingInstitution = reader["IssuingInstitution"].ToString(),
                BeforeOrAfterEmployment = reader["BeforeOrAfterEmployment"].ToString(),
                MilitaryServiceStatus = reader["MilitaryServiceStatus"].ToString(),
                ValidityDate = ParseDateTime(reader["ValidityDate"].ToString()),
                InternalTransferDate = ParseDateTime(reader["InternalTransferDate"].ToString()),
                ExternalTransferDate = ParseDateTime(reader["ExternalTransferDate"].ToString())
            };
        }

        private DateTime? ParseDateTime(string dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return null;
            
            if (DateTime.TryParse(dateString, out DateTime result))
                return result;
            
            return null;
        }

        private int? ParseInt(string intString)
        {
            if (string.IsNullOrEmpty(intString))
                return null;
            
            if (int.TryParse(intString, out int result))
                return result;
            
            return null;
        }
    }
}
