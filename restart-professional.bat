@echo off
chcp 65001 >nul
echo.
echo ========================================
echo  🔄 إعادة تشغيل النظام الاحترافي
echo ========================================
echo.

echo 🛑 إيقاف النسخة السابقة...
taskkill /f /im EmployeeManagementSystem.exe >nul 2>&1
timeout /t 2 >nul

echo 🧹 تنظيف الملفات المؤقتة...
del /q "bin\Release\net8.0-windows\EmployeeManagementSystem.exe" >nul 2>&1
timeout /t 1 >nul

echo 🔨 بناء النسخة المحدثة...
REM تحديث متغيرات البيئة
for /f "tokens=2*" %%a in ('reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SystemPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SystemPath%;%UserPath%"

REM بناء المشروع
dotnet build EmployeeManagementSystem.csproj --configuration Release --verbosity quiet

if %errorlevel% equ 0 (
    echo ✅ تم البناء بنجاح!
    echo.
    echo 🚀 تشغيل النسخة الاحترافية الجديدة...
    echo.
    echo ✨ المميزات الجديدة:
    echo • تصميم Material Design عصري
    echo • لوحة إحصائيات تفاعلية  
    echo • ألوان احترافية متناسقة
    echo • أيقونات حديثة وجذابة
    echo • واجهة بحث محسنة
    echo.
    
    REM تشغيل التطبيق
    dotnet run --project EmployeeManagementSystem.csproj --configuration Release
) else (
    echo ❌ فشل في البناء!
    echo يرجى التأكد من تثبيت .NET 8.0 أو أحدث
    echo.
    pause
)