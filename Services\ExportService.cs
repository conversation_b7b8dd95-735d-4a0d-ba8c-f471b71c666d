using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ClosedXML.Excel;
using EmployeeManagementSystem.Models;

namespace EmployeeManagementSystem.Services
{
    public class ExportService
    {
        public void ExportToExcel(List<User> users, string filePath)
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("بيانات الموظفين");
                
                // إعداد اتجاه النص من اليمين لليسار
                worksheet.RightToLeft = true;
                
                // إنشاء العناوين
                CreateHeaders(worksheet);
                
                // إضافة البيانات
                AddData(worksheet, users);
                
                // تنسيق الجدول
                FormatWorksheet(worksheet, users.Count);
                
                // حفظ الملف
                workbook.SaveAs(filePath);
            }
        }

        private void CreateHeaders(IXLWorksheet worksheet)
        {
            var headers = new string[]
            {
                "الرقم", "رقم القيد", "الرتبة", "الاسم واللقب", "NOM", "PRENOM",
                "تاريخ الميلاد", "العمر", "مكان الميلاد", "الولاية", "رقم عقد الميلاد",
                "رقم التعريف الوطني", "الزمرة الدموية", "إسم الأب", "اسم ولقب الأم",
                "الحالة العائلية", "عدد الأولاد", "اسم ولقب الزوجة", "تاريخ ميلاد الزوجة",
                "مكان ميلاد الزوجة", "تاريخ الزواج", "مهنة الزوجة", "العنوان",
                "رقم الهاتف الشخصي", "البريد الالكتروني", "مكان العمل", "تاريخ التوظيف",
                "أقدمية المهنة", "تاريخ التعيين في الرتبة", "أقدمية الرتبة", "الدرجة الحالية",
                "تاريخ السريان", "نظام العمل", "الوظيفة", "رقم الحساب البريدي",
                "رقم الضمان الاجتماعي", "رقم بطاقة التعريف المهنية", "تاريخ الصدور البطاقة",
                "رقم الإنخراط في التعاضدية", "نوع الشهادة", "التخصص", "رقم الشهادة",
                "تاريخ الصدور", "جهة الاصدار", "قبل او بعد التوظيف", "الوضعية اتجاه الخدمة الوطنية",
                "تاريخ الصلاحية", "تاريخ التحويل الداخلي", "تاريخ التحويل من خارج الولاية"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                var cell = worksheet.Cell(1, i + 1);
                cell.Value = headers[i];
                cell.Style.Font.Bold = true;
                cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            }
        }

        private void AddData(IXLWorksheet worksheet, List<User> users)
        {
            int row = 2;
            
            foreach (var user in users)
            {
                worksheet.Cell(row, 1).Value = user.Id;
                worksheet.Cell(row, 2).Value = user.RegistrationNumber ?? "";
                worksheet.Cell(row, 3).Value = user.Rank ?? "";
                worksheet.Cell(row, 4).Value = user.FullNameArabic ?? "";
                worksheet.Cell(row, 5).Value = user.LastName ?? "";
                worksheet.Cell(row, 6).Value = user.FirstName ?? "";
                worksheet.Cell(row, 7).Value = user.DateOfBirth?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 8).Value = user.Age;
                worksheet.Cell(row, 9).Value = user.PlaceOfBirth ?? "";
                worksheet.Cell(row, 10).Value = user.Province ?? "";
                worksheet.Cell(row, 11).Value = user.BirthCertificateNumber ?? "";
                worksheet.Cell(row, 12).Value = user.NationalId ?? "";
                worksheet.Cell(row, 13).Value = user.BloodType ?? "";
                worksheet.Cell(row, 14).Value = user.FatherName ?? "";
                worksheet.Cell(row, 15).Value = user.MotherName ?? "";
                worksheet.Cell(row, 16).Value = user.MaritalStatus ?? "";
                worksheet.Cell(row, 17).Value = user.NumberOfChildren ?? 0;
                worksheet.Cell(row, 18).Value = user.SpouseName ?? "";
                worksheet.Cell(row, 19).Value = user.SpouseDateOfBirth?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 20).Value = user.SpousePlaceOfBirth ?? "";
                worksheet.Cell(row, 21).Value = user.MarriageDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 22).Value = user.SpouseOccupation ?? "";
                worksheet.Cell(row, 23).Value = user.Address ?? "";
                worksheet.Cell(row, 24).Value = user.PersonalPhone ?? "";
                worksheet.Cell(row, 25).Value = user.Email ?? "";
                worksheet.Cell(row, 26).Value = user.WorkPlace ?? "";
                worksheet.Cell(row, 27).Value = user.EmploymentDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 28).Value = user.JobSeniority;
                worksheet.Cell(row, 29).Value = user.RankAppointmentDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 30).Value = user.RankSeniority;
                worksheet.Cell(row, 31).Value = user.CurrentGrade ?? "";
                worksheet.Cell(row, 32).Value = user.EffectiveDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 33).Value = user.WorkSystem ?? "";
                worksheet.Cell(row, 34).Value = user.Position ?? "";
                worksheet.Cell(row, 35).Value = user.PostalAccountNumber ?? "";
                worksheet.Cell(row, 36).Value = user.SocialSecurityNumber ?? "";
                worksheet.Cell(row, 37).Value = user.ProfessionalIdNumber ?? "";
                worksheet.Cell(row, 38).Value = user.IdCardIssueDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 39).Value = user.MutualInsuranceNumber ?? "";
                worksheet.Cell(row, 40).Value = user.CertificateType ?? "";
                worksheet.Cell(row, 41).Value = user.Specialization ?? "";
                worksheet.Cell(row, 42).Value = user.CertificateNumber ?? "";
                worksheet.Cell(row, 43).Value = user.CertificateIssueDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 44).Value = user.IssuingInstitution ?? "";
                worksheet.Cell(row, 45).Value = user.BeforeOrAfterEmployment ?? "";
                worksheet.Cell(row, 46).Value = user.MilitaryServiceStatus ?? "";
                worksheet.Cell(row, 47).Value = user.ValidityDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 48).Value = user.InternalTransferDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 49).Value = user.ExternalTransferDate?.ToString("dd/MM/yyyy") ?? "";
                
                row++;
            }
        }

        private void FormatWorksheet(IXLWorksheet worksheet, int dataRowCount)
        {
            // تحديد نطاق البيانات
            var dataRange = worksheet.Range(1, 1, dataRowCount + 1, 49);
            
            // إضافة حدود للجدول
            dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            
            // تنسيق النص
            dataRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            dataRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
            
            // تعديل عرض الأعمدة تلقائياً
            worksheet.Columns().AdjustToContents();
            
            // تجميد الصف الأول
            worksheet.SheetView.FreezeRows(1);
            
            // إضافة فلتر تلقائي
            dataRange.SetAutoFilter();
        }

        public void ExportFilteredData(List<User> users, string filePath, string filterDescription = "")
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("النتائج المفلترة");
                worksheet.RightToLeft = true;
                
                // إضافة وصف الفلتر إذا كان متوفراً
                if (!string.IsNullOrEmpty(filterDescription))
                {
                    worksheet.Cell(1, 1).Value = $"معايير البحث: {filterDescription}";
                    worksheet.Cell(1, 1).Style.Font.Bold = true;
                    worksheet.Cell(1, 1).Style.Font.FontSize = 14;
                    worksheet.Range(1, 1, 1, 10).Merge();
                    
                    worksheet.Cell(2, 1).Value = $"عدد النتائج: {users.Count}";
                    worksheet.Cell(2, 1).Style.Font.Bold = true;
                    
                    // بدء البيانات من الصف الرابع
                    CreateHeadersAtRow(worksheet, 4);
                    AddDataFromRow(worksheet, users, 5);
                    FormatWorksheetFromRow(worksheet, users.Count, 4);
                }
                else
                {
                    CreateHeaders(worksheet);
                    AddData(worksheet, users);
                    FormatWorksheet(worksheet, users.Count);
                }
                
                workbook.SaveAs(filePath);
            }
        }

        private void CreateHeadersAtRow(IXLWorksheet worksheet, int startRow)
        {
            var headers = new string[]
            {
                "الرقم", "رقم القيد", "الرتبة", "الاسم واللقب", "رقم التعريف الوطني",
                "العمر", "الولاية", "مكان العمل", "الوظيفة", "تاريخ التوظيف",
                "أقدمية المهنة", "أقدمية الرتبة", "الهاتف", "البريد الإلكتروني"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                var cell = worksheet.Cell(startRow, i + 1);
                cell.Value = headers[i];
                cell.Style.Font.Bold = true;
                cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            }
        }

        private void AddDataFromRow(IXLWorksheet worksheet, List<User> users, int startRow)
        {
            int row = startRow;
            
            foreach (var user in users)
            {
                worksheet.Cell(row, 1).Value = user.Id;
                worksheet.Cell(row, 2).Value = user.RegistrationNumber ?? "";
                worksheet.Cell(row, 3).Value = user.Rank ?? "";
                worksheet.Cell(row, 4).Value = user.FullNameArabic ?? "";
                worksheet.Cell(row, 5).Value = user.NationalId ?? "";
                worksheet.Cell(row, 6).Value = user.Age;
                worksheet.Cell(row, 7).Value = user.Province ?? "";
                worksheet.Cell(row, 8).Value = user.WorkPlace ?? "";
                worksheet.Cell(row, 9).Value = user.Position ?? "";
                worksheet.Cell(row, 10).Value = user.EmploymentDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cell(row, 11).Value = user.JobSeniority;
                worksheet.Cell(row, 12).Value = user.RankSeniority;
                worksheet.Cell(row, 13).Value = user.PersonalPhone ?? "";
                worksheet.Cell(row, 14).Value = user.Email ?? "";
                
                row++;
            }
        }

        private void FormatWorksheetFromRow(IXLWorksheet worksheet, int dataRowCount, int startRow)
        {
            var dataRange = worksheet.Range(startRow, 1, startRow + dataRowCount, 14);
            
            dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            dataRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            dataRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
            
            worksheet.Columns().AdjustToContents();
            worksheet.SheetView.FreezeRows(startRow);
            dataRange.SetAutoFilter();
        }
    }
}
