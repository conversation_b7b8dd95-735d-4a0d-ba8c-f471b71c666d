# دليل البدء السريع 🚀

## خطوات التشغيل السريع

### 1. التحقق من المتطلبات ⚙️
```bash
# افتح Command Prompt واكتب:
dotnet --version
```

**إذا ظهر رقم الإصدار** ✅ → انتقل للخطوة 2  
**إذا ظهر خطأ** ❌ → ثبت .NET من [هنا](https://dotnet.microsoft.com/download)

### 2. اختبار النظام 🧪
```bash
# انقر نقرة مزدوجة على:
test-app.bat
```

**إذا ظهرت نافذة الاختبار** ✅ → النظام يعمل!  
**إذا لم تظهر** ❌ → راجع [دليل استكشاف الأخطاء](TROUBLESHOOTING.md)

### 3. تشغيل النسخة المبسطة 🎯
```bash
# انقر نقرة مزدوجة على:
run-simple.bat
```

### 4. تشغيل النسخة الكاملة 💎
```bash
# انقر نقرة مزدوجة على:
run.bat
```

## الاستخدام الأساسي

### إضافة بيانات الموظفين 📊

#### الطريقة 1: استيراد من Excel
1. حضر ملف Excel بالتنسيق التالي:
   ```
   العمود A: الرقم
   العمود B: رقم القيد  
   العمود C: الرتبة
   العمود D: الاسم واللقب
   العمود E: NOM
   العمود F: PRENOM
   ... (46 عمود كما هو مطلوب)
   ```

2. في التطبيق:
   - انقر "استيراد Excel"
   - اختر الملف
   - انتظر انتهاء الاستيراد

#### الطريقة 2: إدخال يدوي (للمستقبل)
- سيتم إضافة هذه الميزة في الإصدارات القادمة

### البحث عن الموظفين 🔍

#### البحث السريع:
1. اكتب في مربع البحث:
   - اسم الموظف
   - رقم التعريف الوطني
   - الرتبة
   - الولاية
2. اضغط Enter أو انقر "بحث"

#### البحث المتقدم:
1. انقر "بحث متقدم"
2. املأ المعايير المطلوبة
3. انقر "بحث"

### طباعة بطاقة الموظف 🖨️
1. اختر الموظف من الجدول
2. انقر "طباعة البطاقة" أو انقر نقرة مزدوجة
3. راجع المعاينة
4. انقر "طباعة"

### تصدير البيانات 📤
1. انقر "تصدير Excel"
2. اختر مكان الحفظ
3. انتظر انتهاء التصدير

## نصائح سريعة 💡

### للحصول على أفضل أداء:
- ✅ استخدم ملفات Excel صغيرة (أقل من 1000 صف)
- ✅ تأكد من تنسيق التواريخ: DD/MM/YYYY
- ✅ تأكد من عدم وجود صفوف فارغة في Excel
- ✅ احفظ نسخة احتياطية من قاعدة البيانات

### للبحث السريع:
- 🔍 استخدم رقم التعريف الوطني للبحث الدقيق
- 🔍 استخدم جزء من الاسم للبحث العام
- 🔍 استخدم البحث المتقدم للفلترة المعقدة

### لحل المشاكل:
- 🔧 أعد تشغيل التطبيق
- 🔧 تأكد من إغلاق ملفات Excel قبل الاستيراد
- 🔧 شغل التطبيق كمدير إذا ظهرت مشاكل في الصلاحيات

## الملفات المهمة 📁

```
📁 نظام إدارة الموظفين/
├── 🚀 test-app.bat          # اختبار سريع
├── 🎯 run-simple.bat        # النسخة المبسطة  
├── 💎 run.bat               # النسخة الكاملة
├── 📖 README.md             # الدليل الكامل
├── 🔧 TROUBLESHOOTING.md    # حل المشاكل
├── 💾 EmployeeDatabase.sqlite # قاعدة البيانات (تُنشأ تلقائياً)
└── 📊 Models/               # نماذج البيانات
```

## الميزات الرئيسية ⭐

| الميزة | الوصف | الحالة |
|--------|--------|---------|
| 📊 إدارة 46 عمود | جميع البيانات المطلوبة | ✅ جاهز |
| 📈 حساب العمر | تلقائي من تاريخ الميلاد | ✅ جاهز |
| 📈 حساب الأقدمية | المهنة والرتبة | ✅ جاهز |
| 🔍 البحث السريع | في الحقول الرئيسية | ✅ جاهز |
| 🔍 البحث المتقدم | فلترة متعددة المعايير | ✅ جاهز |
| 📥 استيراد Excel | ملفات .xlsx و .xls | ✅ جاهز |
| 📤 تصدير Excel | مع التنسيق العربي | ✅ جاهز |
| 🖨️ طباعة البطاقات | مع QR Code | ✅ جاهز |
| 🗄️ قاعدة بيانات | SQLite محلية | ✅ جاهز |

## الدعم والمساعدة 🆘

### إذا واجهت مشكلة:
1. 📖 راجع [دليل استكشاف الأخطاء](TROUBLESHOOTING.md)
2. 🧪 جرب تطبيق الاختبار: `test-app.bat`
3. 🎯 جرب النسخة المبسطة: `run-simple.bat`
4. 💬 اتصل بالدعم الفني مع تفاصيل الخطأ

### معلومات مفيدة للدعم:
```bash
# معلومات النظام
systeminfo | findstr /B /C:"OS Name"

# إصدار .NET
dotnet --version

# اختبار بسيط
echo "اختبار النظام" > test.txt
```

---

## 🎉 مبروك! 

إذا وصلت هنا، فأنت جاهز لاستخدام نظام إدارة معلومات الموظفين!

**ابدأ بـ:** `test-app.bat` → `run-simple.bat` → `run.bat`
