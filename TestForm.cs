using System;
using System.Windows.Forms;
using System.Drawing;

namespace EmployeeManagementSystem
{
    public class TestForm : Form
    {
        private Label welcomeLabel;
        private Button testButton;
        private TextBox testTextBox;

        public TestForm()
        {
            InitializeForm();
        }

        private void InitializeForm()
        {
            // إعدادات النافذة
            this.Text = "اختبار نظام إدارة معلومات الموظفين";
            this.Size = new Size(500, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;

            // تسمية الترحيب
            welcomeLabel = new Label()
            {
                Text = "مرحباً بك في نظام إدارة معلومات الموظفين",
                Location = new Point(50, 50),
                Size = new Size(400, 30),
                Font = new Font("Arial", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(welcomeLabel);

            // مربع نص للاختبار
            testTextBox = new TextBox()
            {
                Location = new Point(50, 100),
                Size = new Size(300, 25),
                Font = new Font("Arial", 10),
                Text = "اكتب هنا للاختبار..."
            };
            this.Controls.Add(testTextBox);

            // زر الاختبار
            testButton = new Button()
            {
                Text = "اختبار",
                Location = new Point(50, 150),
                Size = new Size(100, 30),
                Font = new Font("Arial", 10)
            };
            testButton.Click += TestButton_Click;
            this.Controls.Add(testButton);

            // معلومات النظام
            var infoLabel = new Label()
            {
                Text = "إذا ظهرت هذه النافذة، فإن النظام يعمل بشكل صحيح!",
                Location = new Point(50, 200),
                Size = new Size(400, 20),
                Font = new Font("Arial", 9),
                ForeColor = Color.Green
            };
            this.Controls.Add(infoLabel);
        }

        private void TestButton_Click(object sender, EventArgs e)
        {
            string message = $"تم النقر على الزر!\nالنص المدخل: {testTextBox.Text}";
            MessageBox.Show(message, "اختبار ناجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    // برنامج اختبار بسيط
    internal static class TestProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                MessageBox.Show("جاري تشغيل نظام الاختبار...", "بدء التشغيل", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                Application.Run(new TestForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
