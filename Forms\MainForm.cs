using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using EmployeeManagementSystem.Data;
using EmployeeManagementSystem.Models;
using EmployeeManagementSystem.Services;

namespace EmployeeManagementSystem.Forms
{
    public class MainForm : Form
    {
        private DatabaseManager databaseManager;
        private ExcelImportService excelImportService;
        private CardPrintService cardPrintService;
        private ExportService exportService;
        private List<User> allUsers;
        private List<User> filteredUsers;

        // عناصر الواجهة الرئيسية
        private MenuStrip menuStrip;
        private Panel headerPanel;
        private Panel mainPanel;
        private Panel buttonsPanel;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        
        // أزرار إدارة الموظفين
        private Button addEmployeeButton;
        private Button editEmployeeButton;
        private Button deleteEmployeeButton;
        private Button viewRecordButton;
        private Button printCardButton;
        private Button importExcelButton;
        private Button exportButton;
        
        // مربع البحث وعرض النتائج
        private TextBox searchTextBox;
        private ListBox employeesListBox;
        private Label selectedEmployeeInfo;
        
        public MainForm()
        {
            InitializeComponent();
            InitializeServices();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "🏢 نظام إدارة معلومات الموظفين - الجلفة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Icon = CreateIcon();
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Cairo", 12F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            SetModernWindowStyle();
            CreateMenuStrip();
            CreateHeaderPanel();
            CreateMainPanel();
            CreateButtonsPanel();
            CreateModernStatusStrip();
        }

        private Icon CreateIcon()
        {
            var bitmap = new Bitmap(32, 32);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.FillRectangle(new SolidBrush(Color.FromArgb(33, 150, 243)), 0, 0, 32, 32);
                g.FillRectangle(Brushes.White, 8, 8, 16, 16);
            }
            return Icon.FromHandle(bitmap.GetHicon());
        }

        private void SetModernWindowStyle()
        {
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.BackColor = Color.FromArgb(250, 250, 250);
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();
            menuStrip.BackColor = Color.FromArgb(33, 150, 243);
            menuStrip.ForeColor = Color.White;
            menuStrip.Font = new Font("Cairo", 14F, FontStyle.Bold);
            menuStrip.Padding = new Padding(10, 10, 10, 10);
            menuStrip.RightToLeft = RightToLeft.Yes;
            
            // قائمة الملف
            var fileMenu = new ToolStripMenuItem("📁 ملف");
            fileMenu.ForeColor = Color.White;
            fileMenu.Font = new Font("Cairo", 13F, FontStyle.Bold);
            fileMenu.DropDownItems.Add("📊 استيراد من Excel", null, ImportExcel_Click);
            fileMenu.DropDownItems.Add("📤 تصدير إلى Excel", null, ExportToExcel_Click);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("🚪 خروج", null, (s, e) => Application.Exit());
            
            // قائمة الموظفين
            var employeeMenu = new ToolStripMenuItem("👥 الموظفين");
            employeeMenu.ForeColor = Color.White;
            employeeMenu.Font = new Font("Cairo", 13F, FontStyle.Bold);
            employeeMenu.DropDownItems.Add("➕ إضافة موظف", null, AddEmployee_Click);
            employeeMenu.DropDownItems.Add("✏️ تعديل موظف", null, EditEmployee_Click);
            employeeMenu.DropDownItems.Add("🗑️ حذف موظف", null, DeleteEmployee_Click);
            employeeMenu.DropDownItems.Add(new ToolStripSeparator());
            employeeMenu.DropDownItems.Add("📋 عرض السجل الكامل", null, ViewRecord_Click);
            
            // قائمة الطباعة
            var printMenu = new ToolStripMenuItem("🖨️ طباعة");
            printMenu.ForeColor = Color.White;
            printMenu.Font = new Font("Cairo", 13F, FontStyle.Bold);
            printMenu.DropDownItems.Add("🆔 طباعة بطاقة الموظف", null, PrintCard_Click);
            
            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("❓ مساعدة");
            helpMenu.ForeColor = Color.White;
            helpMenu.Font = new Font("Cairo", 13F, FontStyle.Bold);
            helpMenu.DropDownItems.Add("ℹ️ حول البرنامج", null, About_Click);
            
            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, employeeMenu, printMenu, helpMenu });
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel();
            headerPanel.Height = 120;
            headerPanel.Dock = DockStyle.Top;
            headerPanel.BackColor = Color.FromArgb(55, 71, 79);
            
            // عنوان رئيسي
            var titleLabel = new Label();
            titleLabel.Text = "🏢 نظام إدارة معلومات الموظفين";
            titleLabel.Font = new Font("Cairo", 24F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(50, 20);
            titleLabel.AutoSize = true;
            titleLabel.RightToLeft = RightToLeft.Yes;
            
            // عنوان فرعي
            var subtitleLabel = new Label();
            subtitleLabel.Text = "🎖️ مديرية الحماية المدنية لولاية الجلفة";
            subtitleLabel.Font = new Font("Cairo", 16F, FontStyle.Regular);
            subtitleLabel.ForeColor = Color.FromArgb(200, 200, 200);
            subtitleLabel.Location = new Point(50, 70);
            subtitleLabel.AutoSize = true;
            subtitleLabel.RightToLeft = RightToLeft.Yes;
            
            headerPanel.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
            this.Controls.Add(headerPanel);
        }

        private void CreateMainPanel()
        {
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.BackColor = Color.White;
            mainPanel.Padding = new Padding(30);
            
            // بحث
            var searchLabel = new Label();
            searchLabel.Text = "🔍 البحث عن موظف:";
            searchLabel.Font = new Font("Cairo", 16F, FontStyle.Bold);
            searchLabel.ForeColor = Color.FromArgb(55, 71, 79);
            searchLabel.Location = new Point(30, 30);
            searchLabel.AutoSize = true;
            searchLabel.RightToLeft = RightToLeft.Yes;
            
            searchTextBox = new TextBox();
            searchTextBox.Size = new Size(500, 40);
            searchTextBox.Location = new Point(30, 70);
            searchTextBox.Font = new Font("Cairo", 14F);
            searchTextBox.BorderStyle = BorderStyle.FixedSingle;
            searchTextBox.BackColor = Color.FromArgb(248, 248, 248);
            searchTextBox.RightToLeft = RightToLeft.Yes;
            searchTextBox.TextAlign = HorizontalAlignment.Right;
            searchTextBox.PlaceholderText = "ابحث بالاسم، رقم القيد، أو رقم التعريف الوطني...";
            searchTextBox.TextChanged += SearchTextBox_TextChanged;
            
            // قائمة الموظفين
            var employeesLabel = new Label();
            employeesLabel.Text = "👥 قائمة الموظفين:";
            employeesLabel.Font = new Font("Cairo", 16F, FontStyle.Bold);
            employeesLabel.ForeColor = Color.FromArgb(55, 71, 79);
            employeesLabel.Location = new Point(30, 130);
            employeesLabel.AutoSize = true;
            employeesLabel.RightToLeft = RightToLeft.Yes;
            
            employeesListBox = new ListBox();
            employeesListBox.Size = new Size(700, 300);
            employeesListBox.Location = new Point(30, 170);
            employeesListBox.Font = new Font("Cairo", 13F);
            employeesListBox.BackColor = Color.FromArgb(248, 248, 248);
            employeesListBox.BorderStyle = BorderStyle.FixedSingle;
            employeesListBox.RightToLeft = RightToLeft.Yes;
            employeesListBox.SelectedIndexChanged += EmployeesListBox_SelectedIndexChanged;
            
            // معلومات الموظف المحدد
            selectedEmployeeInfo = new Label();
            selectedEmployeeInfo.Text = "اختر موظفاً من القائمة لعرض معلوماته";
            selectedEmployeeInfo.Size = new Size(700, 80);
            selectedEmployeeInfo.Location = new Point(30, 490);
            selectedEmployeeInfo.Font = new Font("Cairo", 12F);
            selectedEmployeeInfo.ForeColor = Color.FromArgb(76, 175, 80);
            selectedEmployeeInfo.BackColor = Color.FromArgb(248, 248, 248);
            selectedEmployeeInfo.BorderStyle = BorderStyle.FixedSingle;
            selectedEmployeeInfo.TextAlign = ContentAlignment.MiddleCenter;
            selectedEmployeeInfo.RightToLeft = RightToLeft.Yes;
            
            mainPanel.Controls.AddRange(new Control[] { 
                searchLabel, searchTextBox, employeesLabel, 
                employeesListBox, selectedEmployeeInfo 
            });
            
            this.Controls.Add(mainPanel);
        }

        private void CreateButtonsPanel()
        {
            buttonsPanel = new Panel();
            buttonsPanel.Height = 100;
            buttonsPanel.Dock = DockStyle.Bottom;
            buttonsPanel.BackColor = Color.FromArgb(245, 245, 245);
            buttonsPanel.Padding = new Padding(30, 15, 30, 15);
            
            // الصف الأول من الأزرار
            int buttonWidth = 160;
            int buttonHeight = 50;
            int spacing = 20;
            int startX = 30;
            int y1 = 15;
            
            // أزرار إدارة الموظفين
            addEmployeeButton = CreateButton("➕ إضافة موظف", Color.FromArgb(76, 175, 80), startX, y1, buttonWidth, buttonHeight);
            addEmployeeButton.Click += AddEmployee_Click;
            
            editEmployeeButton = CreateButton("✏️ تعديل موظف", Color.FromArgb(255, 152, 0), startX + (buttonWidth + spacing), y1, buttonWidth, buttonHeight);
            editEmployeeButton.Click += EditEmployee_Click;
            
            deleteEmployeeButton = CreateButton("🗑️ حذف موظف", Color.FromArgb(244, 67, 54), startX + 2 * (buttonWidth + spacing), y1, buttonWidth, buttonHeight);
            deleteEmployeeButton.Click += DeleteEmployee_Click;
            
            viewRecordButton = CreateButton("📋 السجل الكامل", Color.FromArgb(33, 150, 243), startX + 3 * (buttonWidth + spacing), y1, buttonWidth, buttonHeight);
            viewRecordButton.Click += ViewRecord_Click;
            
            printCardButton = CreateButton("🖨️ طباعة البطاقة", Color.FromArgb(156, 39, 176), startX + 4 * (buttonWidth + spacing), y1, buttonWidth, buttonHeight);
            printCardButton.Click += PrintCard_Click;
            
            // الصف الثاني (إذا احتجنا)
            importExcelButton = CreateButton("📊 استيراد Excel", Color.FromArgb(96, 125, 139), startX + 5 * (buttonWidth + spacing), y1, buttonWidth, buttonHeight);
            importExcelButton.Click += ImportExcel_Click;
            
            exportButton = CreateButton("📤 تصدير Excel", Color.FromArgb(121, 85, 72), startX + 6 * (buttonWidth + spacing), y1, buttonWidth, buttonHeight);
            exportButton.Click += ExportToExcel_Click;
            
            buttonsPanel.Controls.AddRange(new Control[] {
                addEmployeeButton, editEmployeeButton, deleteEmployeeButton,
                viewRecordButton, printCardButton, importExcelButton, exportButton
            });
            
            this.Controls.Add(buttonsPanel);
        }

        private Button CreateButton(string text, Color backColor, int x, int y, int width, int height)
        {
            var button = new Button();
            button.Text = text;
            button.Size = new Size(width, height);
            button.Location = new Point(x, y);
            button.BackColor = backColor;
            button.ForeColor = Color.White;
            button.Font = new Font("Cairo", 12F, FontStyle.Bold);
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.RightToLeft = RightToLeft.Yes;
            button.UseVisualStyleBackColor = false;
            
            // تأثير عند التمرير
            button.MouseEnter += (s, e) => {
                button.BackColor = ControlPaint.Light(backColor, 0.2f);
            };
            button.MouseLeave += (s, e) => {
                button.BackColor = backColor;
            };
            
            return button;
        }

        private void CreateModernStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusStrip.BackColor = Color.FromArgb(55, 71, 79);
            statusStrip.ForeColor = Color.White;
            statusStrip.Font = new Font("Cairo", 11F, FontStyle.Regular);
            statusStrip.RightToLeft = RightToLeft.Yes;
            
            statusLabel = new ToolStripStatusLabel();
            statusLabel.Text = "🟢 النظام جاهز - عدد الموظفين: 0";
            statusLabel.ForeColor = Color.White;
            statusLabel.Font = new Font("Cairo", 11F, FontStyle.Regular);
            
            var versionLabel = new ToolStripStatusLabel();
            versionLabel.Text = "الإصدار 2.3 - السجل الكامل";
            versionLabel.ForeColor = Color.FromArgb(200, 200, 200);
            versionLabel.Font = new Font("Cairo", 10F, FontStyle.Regular);
            
            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, versionLabel });
            this.Controls.Add(statusStrip);
        }

        private void InitializeServices()
        {
            databaseManager = new DatabaseManager();
            excelImportService = new ExcelImportService(databaseManager);
            cardPrintService = new CardPrintService();
            exportService = new ExportService();
        }

        private void LoadData()
        {
            try
            {
                allUsers = databaseManager.GetAllUsers();
                filteredUsers = new List<User>(allUsers);
                UpdateEmployeesList();
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateEmployeesList()
        {
            employeesListBox.Items.Clear();
            
            foreach (var user in filteredUsers)
            {
                string displayText = $"{user.Id:D3} - {user.FullNameArabic} - {user.Rank} - {user.RegistrationNumber}";
                employeesListBox.Items.Add(displayText);
            }
        }

        private void UpdateStatusBar()
        {
            statusLabel.Text = $"🟢 النظام جاهز - عدد الموظفين: {allUsers?.Count ?? 0}";
        }

        private void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            string searchTerm = searchTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                filteredUsers = new List<User>(allUsers);
            }
            else
            {
                filteredUsers = allUsers.Where(user =>
                    (user.FullNameArabic != null && user.FullNameArabic.Contains(searchTerm)) ||
                    (user.RegistrationNumber != null && user.RegistrationNumber.Contains(searchTerm)) ||
                    (user.NationalId != null && user.NationalId.Contains(searchTerm)) ||
                    (user.Rank != null && user.Rank.Contains(searchTerm))
                ).ToList();
            }
            
            UpdateEmployeesList();
        }

        private void EmployeesListBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (employeesListBox.SelectedIndex >= 0 && employeesListBox.SelectedIndex < filteredUsers.Count)
            {
                var selectedUser = filteredUsers[employeesListBox.SelectedIndex];
                selectedEmployeeInfo.Text = $"👤 {selectedUser.FullNameArabic}\n" +
                                          $"📅 العمر: {selectedUser.Age} سنة | 💼 الأقدمية: {selectedUser.JobSeniority} | 🎖️ الرتبة: {selectedUser.Rank}";
                selectedEmployeeInfo.BackColor = Color.FromArgb(232, 245, 233);
            }
            else
            {
                selectedEmployeeInfo.Text = "اختر موظفاً من القائمة لعرض معلوماته";
                selectedEmployeeInfo.BackColor = Color.FromArgb(248, 248, 248);
            }
        }

        private User GetSelectedEmployee()
        {
            if (employeesListBox.SelectedIndex >= 0 && employeesListBox.SelectedIndex < filteredUsers.Count)
            {
                return filteredUsers[employeesListBox.SelectedIndex];
            }
            return null;
        }

        #region أحداث الأزرار

        private void AddEmployee_Click(object sender, EventArgs e)
        {
            MessageBox.Show("🚧 وظيفة إضافة موظف قيد التطوير", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditEmployee_Click(object sender, EventArgs e)
        {
            var selectedEmployee = GetSelectedEmployee();
            if (selectedEmployee == null)
            {
                MessageBox.Show("يرجى اختيار موظف من القائمة أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            MessageBox.Show($"🚧 وظيفة تعديل الموظف '{selectedEmployee.FullNameArabic}' قيد التطوير", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteEmployee_Click(object sender, EventArgs e)
        {
            var selectedEmployee = GetSelectedEmployee();
            if (selectedEmployee == null)
            {
                MessageBox.Show("يرجى اختيار موظف من القائمة أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            var result = MessageBox.Show($"هل أنت متأكد من حذف الموظف '{selectedEmployee.FullNameArabic}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
            if (result == DialogResult.Yes)
            {
                MessageBox.Show("🚧 وظيفة حذف الموظف قيد التطوير", "قيد التطوير",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ViewRecord_Click(object sender, EventArgs e)
        {
            var selectedEmployee = GetSelectedEmployee();
            if (selectedEmployee == null)
            {
                MessageBox.Show("يرجى اختيار موظف من القائمة أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            var recordForm = new EmployeeRecordForm(selectedEmployee);
            recordForm.Show();
        }

        private void PrintCard_Click(object sender, EventArgs e)
        {
            var selectedEmployee = GetSelectedEmployee();
            if (selectedEmployee == null)
            {
                MessageBox.Show("يرجى اختيار موظف من القائمة أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            try
            {
                cardPrintService.PrintEmployeeCard(selectedEmployee);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ImportExcel_Click(object sender, EventArgs e)
        {
            var openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "ملفات Excel|*.xlsx;*.xls";
            openFileDialog.Title = "اختر ملف Excel لاستيراده";

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var importedUsers = excelImportService.ImportFromExcel(openFileDialog.FileName);
                    
                    MessageBox.Show($"تم استيراد {importedUsers.Count} موظف بنجاح!", "نجح الاستيراد",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                    LoadData(); // إعادة تحميل البيانات
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في استيراد البيانات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ExportToExcel_Click(object sender, EventArgs e)
        {
            var saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "ملفات Excel|*.xlsx";
            saveFileDialog.Title = "احفظ البيانات كـ Excel";
            saveFileDialog.FileName = $"موظفين_{DateTime.Now:yyyy-MM-dd}.xlsx";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    exportService.ExportToExcel(filteredUsers, saveFileDialog.FileName);
                    MessageBox.Show("تم تصدير البيانات بنجاح!", "نجح التصدير",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void RefreshData_Click(object sender, EventArgs e)
        {
            LoadData();
            MessageBox.Show("تم تحديث البيانات!", "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void About_Click(object sender, EventArgs e)
        {
            string aboutMessage = @"🏢 نظام إدارة معلومات الموظفين
الإصدار 2.3 - السجل الكامل

🎖️ مديرية الحماية المدنية لولاية الجلفة

✨ المميزات:
• إدارة شاملة للموظفين (إضافة، تعديل، حذف)
• السجل الكامل بنمط Access
• طباعة البطاقات الرسمية 
• استيراد وتصدير Excel
• واجهة عربية احترافية

© 2024 - جميع الحقوق محفوظة";

            MessageBox.Show(aboutMessage, "حول البرنامج", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                databaseManager?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}