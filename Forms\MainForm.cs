using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using EmployeeManagementSystem.Data;
using EmployeeManagementSystem.Models;
using EmployeeManagementSystem.Services;

namespace EmployeeManagementSystem.Forms
{
    public partial class MainForm : Form
    {
        private DatabaseManager databaseManager;
        private ExcelImportService excelImportService;
        private CardPrintService cardPrintService;
        private ExportService exportService;
        private List<User> allUsers;
        private List<User> filteredUsers;

        // عناصر الواجهة
        private MenuStrip menuStrip;
        private ToolStrip toolStrip;
        private TextBox searchTextBox;
        private Button searchButton;
        private Button advancedSearchButton;
        private Button importExcelButton;
        private Button printCardButton;
        private Button exportButton;
        private DataGridView dataGridView;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private Panel searchPanel;
        private Label recordCountLabel;

        public MainForm()
        {
            InitializeComponent();
            InitializeServices();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام إدارة معلومات الموظفين";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            // إنشاء القائمة الرئيسية
            CreateMenuStrip();
            
            // إنشاء شريط الأدوات
            CreateToolStrip();
            
            // إنشاء لوحة البحث
            CreateSearchPanel();
            
            // إنشاء جدول البيانات
            CreateDataGridView();
            
            // إنشاء شريط الحالة
            CreateStatusStrip();
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();
            
            // قائمة الملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add("استيراد من Excel", null, ImportExcel_Click);
            fileMenu.DropDownItems.Add("تصدير إلى Excel", null, ExportToExcel_Click);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("خروج", null, (s, e) => Application.Exit());
            
            // قائمة البحث
            var searchMenu = new ToolStripMenuItem("بحث");
            searchMenu.DropDownItems.Add("بحث سريع", null, QuickSearch_Click);
            searchMenu.DropDownItems.Add("بحث متقدم", null, AdvancedSearch_Click);
            searchMenu.DropDownItems.Add("إعادة تحميل البيانات", null, RefreshData_Click);
            
            // قائمة الطباعة
            var printMenu = new ToolStripMenuItem("طباعة");
            printMenu.DropDownItems.Add("طباعة بطاقة الموظف", null, PrintCard_Click);
            printMenu.DropDownItems.Add("طباعة قائمة الموظفين", null, PrintList_Click);
            
            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول البرنامج", null, About_Click);
            
            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, searchMenu, printMenu, helpMenu });
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip();
            toolStrip.ImageScalingSize = new Size(32, 32);
            
            importExcelButton = new ToolStripButton("استيراد Excel");
            importExcelButton.Click += ImportExcel_Click;
            
            var separator1 = new ToolStripSeparator();
            
            var searchLabel = new ToolStripLabel("البحث:");
            
            var searchTextBoxHost = new ToolStripControlHost(new TextBox() { Width = 200 });
            searchTextBox = (TextBox)searchTextBoxHost.Control;
            searchTextBox.KeyDown += SearchTextBox_KeyDown;
            
            searchButton = new ToolStripButton("بحث");
            searchButton.Click += QuickSearch_Click;
            
            advancedSearchButton = new ToolStripButton("بحث متقدم");
            advancedSearchButton.Click += AdvancedSearch_Click;
            
            var separator2 = new ToolStripSeparator();
            
            printCardButton = new ToolStripButton("طباعة البطاقة");
            printCardButton.Click += PrintCard_Click;
            
            exportButton = new ToolStripButton("تصدير");
            exportButton.Click += ExportToExcel_Click;
            
            toolStrip.Items.AddRange(new ToolStripItem[] {
                importExcelButton, separator1, searchLabel, searchTextBoxHost, 
                searchButton, advancedSearchButton, separator2, printCardButton, exportButton
            });
            
            this.Controls.Add(toolStrip);
        }

        private void CreateSearchPanel()
        {
            searchPanel = new Panel();
            searchPanel.Height = 60;
            searchPanel.Dock = DockStyle.Top;
            searchPanel.BackColor = Color.LightGray;
            
            recordCountLabel = new Label();
            recordCountLabel.Text = "عدد السجلات: 0";
            recordCountLabel.Location = new Point(10, 20);
            recordCountLabel.AutoSize = true;
            
            searchPanel.Controls.Add(recordCountLabel);
            this.Controls.Add(searchPanel);
        }

        private void CreateDataGridView()
        {
            dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.ReadOnly = true;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.RightToLeft = RightToLeft.Yes;
            dataGridView.Font = new Font("Arial", 9);
            
            // إضافة معالج النقر المزدوج
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;
            
            this.Controls.Add(dataGridView);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("جاهز");
            statusStrip.Items.Add(statusLabel);
            this.Controls.Add(statusStrip);
        }

        private void InitializeServices()
        {
            databaseManager = new DatabaseManager();
            excelImportService = new ExcelImportService(databaseManager);
            cardPrintService = new CardPrintService();
            exportService = new ExportService();
            allUsers = new List<User>();
            filteredUsers = new List<User>();
        }

        private void LoadData()
        {
            try
            {
                statusLabel.Text = "جاري تحميل البيانات...";
                Application.DoEvents();
                
                allUsers = databaseManager.GetAllUsers();
                filteredUsers = new List<User>(allUsers);
                
                UpdateDataGridView();
                UpdateRecordCount();
                
                statusLabel.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في تحميل البيانات";
            }
        }

        private void UpdateDataGridView()
        {
            var displayData = filteredUsers.Select(u => new
            {
                الرقم = u.Id,
                رقم_القيد = u.RegistrationNumber,
                الرتبة = u.Rank,
                الاسم_الكامل = u.FullNameArabic,
                رقم_التعريف_الوطني = u.NationalId,
                العمر = u.Age,
                الولاية = u.Province,
                مكان_العمل = u.WorkPlace,
                الوظيفة = u.Position,
                تاريخ_التوظيف = u.EmploymentDate?.ToString("dd/MM/yyyy"),
                أقدمية_المهنة = u.JobSeniority,
                أقدمية_الرتبة = u.RankSeniority,
                الهاتف = u.PersonalPhone,
                البريد_الإلكتروني = u.Email
            }).ToList();

            dataGridView.DataSource = displayData;
        }

        private void UpdateRecordCount()
        {
            recordCountLabel.Text = $"عدد السجلات: {filteredUsers.Count} من أصل {allUsers.Count}";
        }

        // معالجات الأحداث
        private void ImportExcel_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Excel Files|*.xlsx;*.xls";
            openFileDialog.Title = "اختر ملف Excel";
            
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    statusLabel.Text = "جاري استيراد البيانات...";
                    Application.DoEvents();
                    
                    var result = excelImportService.ImportFromExcel(openFileDialog.FileName);
                    
                    string message = $"تم استيراد {result.SuccessCount} سجل بنجاح";
                    if (result.HasErrors)
                    {
                        message += $"\nفشل في استيراد {result.ErrorCount} سجل";
                        message += "\n\nالأخطاء:\n" + string.Join("\n", result.ErrorMessages.Take(10));
                    }
                    
                    MessageBox.Show(message, "نتيجة الاستيراد", MessageBoxButtons.OK, 
                        result.HasErrors ? MessageBoxIcon.Warning : MessageBoxIcon.Information);
                    
                    LoadData(); // إعادة تحميل البيانات
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في استيراد الملف: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    statusLabel.Text = "جاهز";
                }
            }
        }

        private void QuickSearch_Click(object sender, EventArgs e)
        {
            PerformQuickSearch();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                PerformQuickSearch();
            }
        }

        private void PerformQuickSearch()
        {
            string searchTerm = searchTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                filteredUsers = new List<User>(allUsers);
            }
            else
            {
                filteredUsers = allUsers.Where(u =>
                    (u.FullNameArabic?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true) ||
                    (u.NationalId?.Contains(searchTerm) == true) ||
                    (u.Rank?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true) ||
                    (u.Province?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true) ||
                    (u.WorkPlace?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)
                ).ToList();
            }
            
            UpdateDataGridView();
            UpdateRecordCount();
        }

        private void AdvancedSearch_Click(object sender, EventArgs e)
        {
            var advancedSearchForm = new AdvancedSearchForm(allUsers);
            if (advancedSearchForm.ShowDialog() == DialogResult.OK)
            {
                filteredUsers = advancedSearchForm.SearchResults;
                UpdateDataGridView();
                UpdateRecordCount();

                statusLabel.Text = $"تم العثور على {filteredUsers.Count} نتيجة";
            }
        }

        private void PrintCard_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار موظف لطباعة بطاقته", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            int selectedId = (int)dataGridView.SelectedRows[0].Cells["الرقم"].Value;
            var selectedUser = allUsers.FirstOrDefault(u => u.Id == selectedId);
            
            if (selectedUser != null)
            {
                cardPrintService.PrintEmployeeCard(selectedUser);
            }
        }

        private void ExportToExcel_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "Excel Files|*.xlsx";
            saveFileDialog.Title = "حفظ ملف Excel";
            saveFileDialog.FileName = $"بيانات_الموظفين_{DateTime.Now:yyyy-MM-dd}";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    statusLabel.Text = "جاري تصدير البيانات...";
                    Application.DoEvents();

                    if (filteredUsers.Count < allUsers.Count)
                    {
                        // تصدير البيانات المفلترة
                        string filterDescription = $"تم تصفية {filteredUsers.Count} من أصل {allUsers.Count} سجل";
                        exportService.ExportFilteredData(filteredUsers, saveFileDialog.FileName, filterDescription);
                    }
                    else
                    {
                        // تصدير جميع البيانات
                        exportService.ExportToExcel(allUsers, saveFileDialog.FileName);
                    }

                    MessageBox.Show($"تم تصدير {filteredUsers.Count} سجل بنجاح إلى:\n{saveFileDialog.FileName}",
                        "تم التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    statusLabel.Text = "تم التصدير بنجاح";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    statusLabel.Text = "خطأ في التصدير";
                }
            }
        }

        private void PrintList_Click(object sender, EventArgs e)
        {
            // سيتم تطوير وظيفة طباعة القائمة لاحقاً
            MessageBox.Show("سيتم تطوير وظيفة طباعة القائمة في الإصدار القادم", "قريباً", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void RefreshData_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void About_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام إدارة معلومات الموظفين\nالإصدار 1.0\n\nتم تطويره باستخدام C# و Windows Forms", 
                "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                PrintCard_Click(sender, e);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                cardPrintService?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
