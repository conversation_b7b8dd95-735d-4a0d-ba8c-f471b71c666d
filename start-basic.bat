@echo off
cls
echo ========================================
echo    نظام إدارة معلومات الموظفين
echo         الإصدار الأساسي
echo ========================================
echo.

echo [1/3] التحقق من متطلبات النظام...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: .NET غير مثبت
    echo.
    echo يرجى تثبيت .NET من الرابط التالي:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)
echo ✅ .NET متوفر

echo.
echo [2/3] بناء التطبيق...
dotnet build BasicApp.csproj --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء التطبيق
    echo.
    echo جاري المحاولة مع تفاصيل أكثر...
    dotnet build BasicApp.csproj
    pause
    exit /b 1
)
echo ✅ تم بناء التطبيق بنجاح

echo.
echo [3/3] تشغيل التطبيق...
echo ✅ جاري فتح النافذة...
echo.

dotnet run --project BasicApp.csproj

echo.
echo تم إغلاق التطبيق بنجاح
echo شكراً لاستخدام النظام!
echo.
pause
