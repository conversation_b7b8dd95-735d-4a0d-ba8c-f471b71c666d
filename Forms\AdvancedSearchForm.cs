using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using EmployeeManagementSystem.Models;

namespace EmployeeManagementSystem.Forms
{
    public class AdvancedSearchForm : Form
    {
        public List<User> SearchResults { get; private set; }
        private List<User> allUsers;

        // عناصر الواجهة
        private TextBox nameTextBox;
        private TextBox nationalIdTextBox;
        private ComboBox rankComboBox;
        private ComboBox provinceComboBox;
        private ComboBox workPlaceComboBox;
        private ComboBox maritalStatusComboBox;
        private DateTimePicker birthDateFromPicker;
        private DateTimePicker birthDateToPicker;
        private DateTimePicker employmentDateFromPicker;
        private DateTimePicker employmentDateToPicker;
        private NumericUpDown ageFromNumeric;
        private NumericUpDown ageToNumeric;
        private CheckBox useBirthDateFilter;
        private CheckBox useEmploymentDateFilter;
        private CheckBox useAgeFilter;
        private Button searchButton;
        private Button clearButton;
        private Button cancelButton;

        public AdvancedSearchForm(List<User> users)
        {
            allUsers = users;
            SearchResults = new List<User>();
            InitializeComponent();
            PopulateComboBoxes();
        }

        private void InitializeComponent()
        {
            this.Text = "البحث المتقدم";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // حقول النص
            nameTextBox = new TextBox() { Width = 200 };
            nationalIdTextBox = new TextBox() { Width = 200 };

            // القوائم المنسدلة
            rankComboBox = new ComboBox() { Width = 200, DropDownStyle = ComboBoxStyle.DropDownList };
            provinceComboBox = new ComboBox() { Width = 200, DropDownStyle = ComboBoxStyle.DropDownList };
            workPlaceComboBox = new ComboBox() { Width = 200, DropDownStyle = ComboBoxStyle.DropDownList };
            maritalStatusComboBox = new ComboBox() { Width = 200, DropDownStyle = ComboBoxStyle.DropDownList };

            // منتقيات التاريخ
            birthDateFromPicker = new DateTimePicker() { Width = 150, Format = DateTimePickerFormat.Short };
            birthDateToPicker = new DateTimePicker() { Width = 150, Format = DateTimePickerFormat.Short };
            employmentDateFromPicker = new DateTimePicker() { Width = 150, Format = DateTimePickerFormat.Short };
            employmentDateToPicker = new DateTimePicker() { Width = 150, Format = DateTimePickerFormat.Short };

            // حقول العمر
            ageFromNumeric = new NumericUpDown() { Width = 80, Minimum = 18, Maximum = 70, Value = 25 };
            ageToNumeric = new NumericUpDown() { Width = 80, Minimum = 18, Maximum = 70, Value = 60 };

            // مربعات الاختيار
            useBirthDateFilter = new CheckBox() { Text = "استخدام فلتر تاريخ الميلاد" };
            useEmploymentDateFilter = new CheckBox() { Text = "استخدام فلتر تاريخ التوظيف" };
            useAgeFilter = new CheckBox() { Text = "استخدام فلتر العمر" };

            // الأزرار
            searchButton = new Button() { Text = "بحث", Width = 80, Height = 30 };
            clearButton = new Button() { Text = "مسح", Width = 80, Height = 30 };
            cancelButton = new Button() { Text = "إلغاء", Width = 80, Height = 30, DialogResult = DialogResult.Cancel };

            // ربط الأحداث
            searchButton.Click += SearchButton_Click;
            clearButton.Click += ClearButton_Click;
            useBirthDateFilter.CheckedChanged += (s, e) => ToggleDatePickers(birthDateFromPicker, birthDateToPicker, useBirthDateFilter.Checked);
            useEmploymentDateFilter.CheckedChanged += (s, e) => ToggleDatePickers(employmentDateFromPicker, employmentDateToPicker, useEmploymentDateFilter.Checked);
            useAgeFilter.CheckedChanged += (s, e) => ToggleAgeControls(useAgeFilter.Checked);

            // تعطيل عناصر التحكم في البداية
            ToggleDatePickers(birthDateFromPicker, birthDateToPicker, false);
            ToggleDatePickers(employmentDateFromPicker, employmentDateToPicker, false);
            ToggleAgeControls(false);
        }

        private void LayoutControls()
        {
            int labelWidth = 120;
            int startX = 20;
            int startY = 20;
            int rowHeight = 35;
            int currentY = startY;

            // الاسم
            this.Controls.Add(new Label() { Text = "الاسم:", Location = new Point(startX, currentY), Width = labelWidth });
            nameTextBox.Location = new Point(startX + labelWidth + 10, currentY - 3);
            this.Controls.Add(nameTextBox);
            currentY += rowHeight;

            // رقم التعريف الوطني
            this.Controls.Add(new Label() { Text = "رقم التعريف الوطني:", Location = new Point(startX, currentY), Width = labelWidth });
            nationalIdTextBox.Location = new Point(startX + labelWidth + 10, currentY - 3);
            this.Controls.Add(nationalIdTextBox);
            currentY += rowHeight;

            // الرتبة
            this.Controls.Add(new Label() { Text = "الرتبة:", Location = new Point(startX, currentY), Width = labelWidth });
            rankComboBox.Location = new Point(startX + labelWidth + 10, currentY - 3);
            this.Controls.Add(rankComboBox);
            currentY += rowHeight;

            // الولاية
            this.Controls.Add(new Label() { Text = "الولاية:", Location = new Point(startX, currentY), Width = labelWidth });
            provinceComboBox.Location = new Point(startX + labelWidth + 10, currentY - 3);
            this.Controls.Add(provinceComboBox);
            currentY += rowHeight;

            // مكان العمل
            this.Controls.Add(new Label() { Text = "مكان العمل:", Location = new Point(startX, currentY), Width = labelWidth });
            workPlaceComboBox.Location = new Point(startX + labelWidth + 10, currentY - 3);
            this.Controls.Add(workPlaceComboBox);
            currentY += rowHeight;

            // الحالة العائلية
            this.Controls.Add(new Label() { Text = "الحالة العائلية:", Location = new Point(startX, currentY), Width = labelWidth });
            maritalStatusComboBox.Location = new Point(startX + labelWidth + 10, currentY - 3);
            this.Controls.Add(maritalStatusComboBox);
            currentY += rowHeight;

            // فلتر تاريخ الميلاد
            useBirthDateFilter.Location = new Point(startX, currentY);
            this.Controls.Add(useBirthDateFilter);
            currentY += 25;

            this.Controls.Add(new Label() { Text = "من:", Location = new Point(startX + 20, currentY), Width = 30 });
            birthDateFromPicker.Location = new Point(startX + 60, currentY - 3);
            this.Controls.Add(birthDateFromPicker);

            this.Controls.Add(new Label() { Text = "إلى:", Location = new Point(startX + 220, currentY), Width = 30 });
            birthDateToPicker.Location = new Point(startX + 260, currentY - 3);
            this.Controls.Add(birthDateToPicker);
            currentY += rowHeight;

            // فلتر تاريخ التوظيف
            useEmploymentDateFilter.Location = new Point(startX, currentY);
            this.Controls.Add(useEmploymentDateFilter);
            currentY += 25;

            this.Controls.Add(new Label() { Text = "من:", Location = new Point(startX + 20, currentY), Width = 30 });
            employmentDateFromPicker.Location = new Point(startX + 60, currentY - 3);
            this.Controls.Add(employmentDateFromPicker);

            this.Controls.Add(new Label() { Text = "إلى:", Location = new Point(startX + 220, currentY), Width = 30 });
            employmentDateToPicker.Location = new Point(startX + 260, currentY - 3);
            this.Controls.Add(employmentDateToPicker);
            currentY += rowHeight;

            // فلتر العمر
            useAgeFilter.Location = new Point(startX, currentY);
            this.Controls.Add(useAgeFilter);
            currentY += 25;

            this.Controls.Add(new Label() { Text = "من:", Location = new Point(startX + 20, currentY), Width = 30 });
            ageFromNumeric.Location = new Point(startX + 60, currentY - 3);
            this.Controls.Add(ageFromNumeric);

            this.Controls.Add(new Label() { Text = "إلى:", Location = new Point(startX + 150, currentY), Width = 30 });
            ageToNumeric.Location = new Point(startX + 190, currentY - 3);
            this.Controls.Add(ageToNumeric);
            currentY += rowHeight + 20;

            // الأزرار
            searchButton.Location = new Point(startX, currentY);
            this.Controls.Add(searchButton);

            clearButton.Location = new Point(startX + 90, currentY);
            this.Controls.Add(clearButton);

            cancelButton.Location = new Point(startX + 180, currentY);
            this.Controls.Add(cancelButton);
        }

        private void PopulateComboBoxes()
        {
            // ملء قائمة الرتب
            var ranks = allUsers.Where(u => !string.IsNullOrEmpty(u.Rank))
                               .Select(u => u.Rank)
                               .Distinct()
                               .OrderBy(r => r)
                               .ToList();
            rankComboBox.Items.Add("الكل");
            rankComboBox.Items.AddRange(ranks.ToArray());
            rankComboBox.SelectedIndex = 0;

            // ملء قائمة الولايات
            var provinces = allUsers.Where(u => !string.IsNullOrEmpty(u.Province))
                                   .Select(u => u.Province)
                                   .Distinct()
                                   .OrderBy(p => p)
                                   .ToList();
            provinceComboBox.Items.Add("الكل");
            provinceComboBox.Items.AddRange(provinces.ToArray());
            provinceComboBox.SelectedIndex = 0;

            // ملء قائمة أماكن العمل
            var workPlaces = allUsers.Where(u => !string.IsNullOrEmpty(u.WorkPlace))
                                    .Select(u => u.WorkPlace)
                                    .Distinct()
                                    .OrderBy(w => w)
                                    .ToList();
            workPlaceComboBox.Items.Add("الكل");
            workPlaceComboBox.Items.AddRange(workPlaces.ToArray());
            workPlaceComboBox.SelectedIndex = 0;

            // ملء قائمة الحالة العائلية
            maritalStatusComboBox.Items.AddRange(new string[] { "الكل", "أعزب", "متزوج", "مطلق", "أرمل" });
            maritalStatusComboBox.SelectedIndex = 0;
        }

        private void ToggleDatePickers(DateTimePicker fromPicker, DateTimePicker toPicker, bool enabled)
        {
            fromPicker.Enabled = enabled;
            toPicker.Enabled = enabled;
        }

        private void ToggleAgeControls(bool enabled)
        {
            ageFromNumeric.Enabled = enabled;
            ageToNumeric.Enabled = enabled;
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            SearchResults = PerformSearch();
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            nameTextBox.Clear();
            nationalIdTextBox.Clear();
            rankComboBox.SelectedIndex = 0;
            provinceComboBox.SelectedIndex = 0;
            workPlaceComboBox.SelectedIndex = 0;
            maritalStatusComboBox.SelectedIndex = 0;
            useBirthDateFilter.Checked = false;
            useEmploymentDateFilter.Checked = false;
            useAgeFilter.Checked = false;
        }

        private List<User> PerformSearch()
        {
            var results = allUsers.AsEnumerable();

            // فلتر الاسم
            if (!string.IsNullOrEmpty(nameTextBox.Text))
            {
                results = results.Where(u => u.FullNameArabic?.Contains(nameTextBox.Text, StringComparison.OrdinalIgnoreCase) == true);
            }

            // فلتر رقم التعريف الوطني
            if (!string.IsNullOrEmpty(nationalIdTextBox.Text))
            {
                results = results.Where(u => u.NationalId?.Contains(nationalIdTextBox.Text) == true);
            }

            // فلتر الرتبة
            if (rankComboBox.SelectedIndex > 0)
            {
                results = results.Where(u => u.Rank == rankComboBox.SelectedItem.ToString());
            }

            // فلتر الولاية
            if (provinceComboBox.SelectedIndex > 0)
            {
                results = results.Where(u => u.Province == provinceComboBox.SelectedItem.ToString());
            }

            // فلتر مكان العمل
            if (workPlaceComboBox.SelectedIndex > 0)
            {
                results = results.Where(u => u.WorkPlace == workPlaceComboBox.SelectedItem.ToString());
            }

            // فلتر الحالة العائلية
            if (maritalStatusComboBox.SelectedIndex > 0)
            {
                results = results.Where(u => u.MaritalStatus == maritalStatusComboBox.SelectedItem.ToString());
            }

            // فلتر تاريخ الميلاد
            if (useBirthDateFilter.Checked)
            {
                results = results.Where(u => u.DateOfBirth.HasValue &&
                    u.DateOfBirth.Value.Date >= birthDateFromPicker.Value.Date &&
                    u.DateOfBirth.Value.Date <= birthDateToPicker.Value.Date);
            }

            // فلتر تاريخ التوظيف
            if (useEmploymentDateFilter.Checked)
            {
                results = results.Where(u => u.EmploymentDate.HasValue &&
                    u.EmploymentDate.Value.Date >= employmentDateFromPicker.Value.Date &&
                    u.EmploymentDate.Value.Date <= employmentDateToPicker.Value.Date);
            }

            // فلتر العمر
            if (useAgeFilter.Checked)
            {
                results = results.Where(u => u.Age >= ageFromNumeric.Value && u.Age <= ageToNumeric.Value);
            }

            return results.ToList();
        }
    }
}
