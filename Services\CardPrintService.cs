using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using QRCoder;
using EmployeeManagementSystem.Models;

namespace EmployeeManagementSystem.Services
{
    public class CardPrintService
    {
        private User? currentUser;
        private PrintDocument printDocument;

        public CardPrintService()
        {
            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
        }

        public void PrintEmployeeCard(User user)
        {
            currentUser = user;
            
            // إعداد الطباعة
            printDocument.DefaultPageSettings.PaperSize = new PaperSize("A4", 827, 1169);
            printDocument.DefaultPageSettings.Margins = new Margins(50, 50, 50, 50);

            // عرض معاينة الطباعة
            PrintPreviewDialog previewDialog = new PrintPreviewDialog();
            previewDialog.Document = printDocument;
            previewDialog.WindowState = FormWindowState.Maximized;
            previewDialog.ShowDialog();
        }

        public void DirectPrint(User user)
        {
            currentUser = user;
            
            PrintDialog printDialog = new PrintDialog();
            printDialog.Document = printDocument;
            
            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                printDocument.Print();
            }
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            if (currentUser == null) return;

            Graphics g = e.Graphics!;
            
            // الخطوط
            Font titleFont = new Font("Arial", 16, FontStyle.Bold);
            Font headerFont = new Font("Arial", 12, FontStyle.Bold);
            Font normalFont = new Font("Arial", 10);
            Font smallFont = new Font("Arial", 8);

            // الألوان
            Brush blackBrush = Brushes.Black;
            Brush blueBrush = Brushes.DarkBlue;
            Pen borderPen = new Pen(Color.Black, 2);

            // إحداثيات البطاقة
            int cardX = 50;
            int cardY = 50;
            int cardWidth = 700;
            int cardHeight = 450;

            // رسم إطار البطاقة
            g.DrawRectangle(borderPen, cardX, cardY, cardWidth, cardHeight);
            
            // رسم خط فاصل علوي
            g.FillRectangle(blueBrush, cardX, cardY, cardWidth, 40);

            // عنوان البطاقة
            string title = "بطاقة معلومات الموظف";
            SizeF titleSize = g.MeasureString(title, titleFont);
            g.DrawString(title, titleFont, Brushes.White, 
                cardX + (cardWidth - titleSize.Width) / 2, cardY + 10);

            // إنشاء QR Code
            Bitmap qrCode = GenerateQRCode(currentUser.GetQRData());
            int qrSize = 120;
            int qrX = cardX + cardWidth - qrSize - 20;
            int qrY = cardY + 60;
            g.DrawImage(qrCode, qrX, qrY, qrSize, qrSize);

            // المعلومات الأساسية
            int startY = cardY + 60;
            int lineHeight = 25;
            int labelX = cardX + 20;
            int valueX = cardX + 200;

            // الصف الأول
            g.DrawString("الاسم الكامل:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.FullNameArabic ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الرتبة:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.Rank ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("رقم التعريف الوطني:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.NationalId ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("تاريخ الميلاد:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.DateOfBirth?.ToString("dd/MM/yyyy") ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("العمر:", headerFont, blackBrush, labelX, startY);
            g.DrawString($"{currentUser.Age} سنة", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("مكان العمل:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.WorkPlace ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الوظيفة:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.Position ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("تاريخ التوظيف:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.EmploymentDate?.ToString("dd/MM/yyyy") ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الأقدمية في المهنة:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.JobSeniority, normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("تاريخ التعيين في الرتبة:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.RankAppointmentDate?.ToString("dd/MM/yyyy") ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("أقدمية الرتبة:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.RankSeniority, normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الهاتف:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.PersonalPhone ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("البريد الإلكتروني:", headerFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.Email ?? "", normalFont, blackBrush, valueX, startY);

            // خط فاصل سفلي
            g.FillRectangle(blueBrush, cardX, cardY + cardHeight - 30, cardWidth, 30);
            
            // تاريخ الطباعة
            string printDate = $"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}";
            SizeF dateSize = g.MeasureString(printDate, smallFont);
            g.DrawString(printDate, smallFont, Brushes.White, 
                cardX + (cardWidth - dateSize.Width) / 2, cardY + cardHeight - 20);

            // تنظيف الموارد
            titleFont.Dispose();
            headerFont.Dispose();
            normalFont.Dispose();
            smallFont.Dispose();
            borderPen.Dispose();
            qrCode.Dispose();
        }

        private Bitmap GenerateQRCode(string data)
        {
            QRCodeGenerator qrGenerator = new QRCodeGenerator();
            QRCodeData qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
            QRCode qrCode = new QRCode(qrCodeData);
            
            return qrCode.GetGraphic(10, Color.Black, Color.White, true);
        }

        public void Dispose()
        {
            printDocument?.Dispose();
        }
    }
}
