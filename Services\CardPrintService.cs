using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using QRCoder;
using EmployeeManagementSystem.Models;

namespace EmployeeManagementSystem.Services
{
    public class CardPrintService
    {
        private User? currentUser;
        private PrintDocument printDocument;

        public CardPrintService()
        {
            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
        }

        public void PrintEmployeeCard(User user)
        {
            currentUser = user;
            
            // إعداد الطباعة
            printDocument.DefaultPageSettings.PaperSize = new PaperSize("A4", 827, 1169);
            printDocument.DefaultPageSettings.Margins = new Margins(50, 50, 50, 50);

            // عرض معاينة الطباعة
            PrintPreviewDialog previewDialog = new PrintPreviewDialog();
            previewDialog.Document = printDocument;
            previewDialog.WindowState = FormWindowState.Maximized;
            previewDialog.ShowDialog();
        }

        public void DirectPrint(User user)
        {
            currentUser = user;
            
            PrintDialog printDialog = new PrintDialog();
            printDialog.Document = printDocument;
            
            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                printDocument.Print();
            }
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            if (currentUser == null) return;

            Graphics g = e.Graphics!;
            
            // الخطوط العربية
            Font headerTitleFont = new Font("Cairo", 14, FontStyle.Bold);
            Font headerFont = new Font("Cairo", 10, FontStyle.Regular);
            Font titleFont = new Font("Cairo", 16, FontStyle.Bold);
            Font labelFont = new Font("Cairo", 11, FontStyle.Bold);
            Font normalFont = new Font("Cairo", 10, FontStyle.Regular);
            Font smallFont = new Font("Cairo", 8, FontStyle.Regular);

            // الألوان
            Brush blackBrush = Brushes.Black;
            Brush blueBrush = Brushes.DarkBlue;
            Pen borderPen = new Pen(Color.Black, 1);

            int yPosition = 40;
            int pageWidth = e.PageBounds.Width;

            // رسم الرأس الرسمي
            g.DrawString("الجمهوريــــــــــــــــــــة الجزائريــــــــــــــــــــة الديمقراطيــــــــــــــــــــة الشعبيــــــــــــــــــــة", 
                headerTitleFont, blackBrush, 150, yPosition);
            yPosition += 30;
            
            g.DrawString("وزارة الداخليــــــــــــــــــــــــــــــــــــــــة والجماعــــــــــــــــــــــــــات", 
                headerFont, blackBrush, 100, yPosition);
            g.DrawString("الجلـفــــــــــــــــــــــة :", 
                headerFont, blackBrush, pageWidth - 150, yPosition);
            yPosition += 25;
            
            g.DrawString("المحليـــــــــــــــــــــــــة والتهيئــــــــــــــــــــــــــة العمرانيــــــــــــــــــــــــــــــة", 
                headerFont, blackBrush, 100, yPosition);
            yPosition += 25;
            
            g.DrawString("المديرية العامـــــــــــــة للحمايــــــــــــــة المدنيـــــــــــــــة", 
                headerFont, blackBrush, 100, yPosition);
            yPosition += 25;
            
            g.DrawString("مديرية الحماية المدنية لولاية الجلفة", 
                headerFont, blackBrush, 100, yPosition);
            yPosition += 25;
            
            g.DrawString("الوحــــــــــــــــــــــــــــــــــــــدة الرئيسيـــــــــــــة بالجلفــــــــــــــــــــــــــــــــة", 
                headerFont, blackBrush, 100, yPosition);
            yPosition += 35;
            
            g.DrawString($"الرقــــــــــــم   :                     و ر ح و ج/ {DateTime.Now.Year}", 
                headerFont, blackBrush, 100, yPosition);
            yPosition += 50;

            // عنوان البطاقة
            string cardTitle = "بطاقة معلومات موظف";
            SizeF titleSize = g.MeasureString(cardTitle, titleFont);
            g.DrawString(cardTitle, titleFont, blueBrush, 
                (pageWidth - titleSize.Width) / 2, yPosition);
            yPosition += 50;

            // إحداثيات البطاقة
            int cardX = 50;
            int cardY = yPosition;
            int cardWidth = 700;
            int cardHeight = 400;

            // رسم إطار البطاقة
            g.DrawRectangle(borderPen, cardX, cardY, cardWidth, cardHeight);
            
            // رسم خط فاصل علوي للبطاقة
            g.FillRectangle(blueBrush, cardX + 1, cardY + 1, cardWidth - 2, 35);

            // إنشاء QR Code
            Bitmap qrCode = GenerateQRCode(currentUser.GetQRData());
            int qrSize = 120;
            int qrX = cardX + cardWidth - qrSize - 20;
            int qrY = cardY + 60;
            g.DrawImage(qrCode, qrX, qrY, qrSize, qrSize);

            // المعلومات الأساسية
            int startY = cardY + 45;
            int lineHeight = 25;
            int labelX = cardX + 20;
            int valueX = cardX + 220;

            // الصف الأول
            g.DrawString("الاسم الكامل:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.FullNameArabic ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الرتبة:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.Rank ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("رقم القيد:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.RegistrationNumber ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("رقم التعريف الوطني:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.NationalId ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("تاريخ الميلاد:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.DateOfBirth?.ToString("dd/MM/yyyy") ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("مكان الميلاد:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.PlaceOfBirth ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("العمر:", labelFont, blackBrush, labelX, startY);
            g.DrawString($"{currentUser.Age} سنة", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("مكان العمل:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.WorkPlace ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الوظيفة:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.Position ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("تاريخ التوظيف:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.EmploymentDate?.ToString("dd/MM/yyyy") ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الأقدمية في المهنة:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.JobSeniority, normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("تاريخ التعيين في الرتبة:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.RankAppointmentDate?.ToString("dd/MM/yyyy") ?? "", normalFont, blackBrush, valueX, startY);

            startY += lineHeight;
            g.DrawString("الهاتف الشخصي:", labelFont, blackBrush, labelX, startY);
            g.DrawString(currentUser.PersonalPhone ?? "", normalFont, blackBrush, valueX, startY);

            // خط فاصل سفلي
            g.FillRectangle(blueBrush, cardX, cardY + cardHeight - 30, cardWidth, 30);
            
            // تاريخ الطباعة والختم
            string printDate = $"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}";
            g.DrawString(printDate, smallFont, Brushes.White, 
                cardX + 20, cardY + cardHeight - 20);
                
            // ختم رسمي
            string officialSeal = "مديرية الحماية المدنية - الجلفة";
            SizeF sealSize = g.MeasureString(officialSeal, smallFont);
            g.DrawString(officialSeal, smallFont, Brushes.White, 
                cardX + cardWidth - sealSize.Width - 20, cardY + cardHeight - 20);

            // تنظيف الموارد
            headerTitleFont.Dispose();
            headerFont.Dispose();
            titleFont.Dispose();
            labelFont.Dispose();
            normalFont.Dispose();
            smallFont.Dispose();
            borderPen.Dispose();
            qrCode.Dispose();
        }

        private Bitmap GenerateQRCode(string data)
        {
            QRCodeGenerator qrGenerator = new QRCodeGenerator();
            QRCodeData qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
            QRCode qrCode = new QRCode(qrCodeData);
            
            return qrCode.GetGraphic(10, Color.Black, Color.White, true);
        }

        public void Dispose()
        {
            printDocument?.Dispose();
        }
    }
}
