using System;
using System.Drawing;
using System.Windows.Forms;
using EmployeeManagementSystem.Models;

namespace EmployeeManagementSystem.Forms
{
    public partial class EmployeeDetailsForm : Form
    {
        private User employee;
        private TabControl tabControl;
        
        public EmployeeDetailsForm(User user)
        {
            employee = user;
            InitializeComponent();
            LoadEmployeeData();
        }

        private void InitializeComponent()
        {
            this.Text = $"📋 تفاصيل الموظف - {employee.FullNameArabic}";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(250, 250, 250);
            this.Font = new Font("Cairo", 10F, FontStyle.Regular);

            CreateHeaderPanel();
            CreateTabControl();
            CreateButtonsPanel();
        }

        private void CreateHeaderPanel()
        {
            var headerPanel = new Panel();
            headerPanel.Height = 80;
            headerPanel.Dock = DockStyle.Top;
            headerPanel.BackColor = Color.FromArgb(55, 71, 79);
            
            var titleLabel = new Label();
            titleLabel.Text = $"👤 {employee.FullNameArabic ?? "موظف غير مسمى"}";
            titleLabel.Font = new Font("Cairo", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(20, 15);
            titleLabel.AutoSize = true;
            titleLabel.RightToLeft = RightToLeft.Yes;
            
            var subtitleLabel = new Label();
            subtitleLabel.Text = $"الرتبة: {employee.Rank ?? "غير محدد"} | العمر: {employee.Age} سنة";
            subtitleLabel.Font = new Font("Cairo", 11F, FontStyle.Regular);
            subtitleLabel.ForeColor = Color.FromArgb(200, 200, 200);
            subtitleLabel.Location = new Point(20, 50);
            subtitleLabel.AutoSize = true;
            subtitleLabel.RightToLeft = RightToLeft.Yes;
            
            headerPanel.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
            this.Controls.Add(headerPanel);
        }

        private void CreateTabControl()
        {
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Cairo", 11F, FontStyle.Bold);
            tabControl.RightToLeft = RightToLeft.Yes;
            tabControl.RightToLeftLayout = true;
            
            // تبويب المعلومات الأساسية
            var basicInfoTab = new TabPage("📄 المعلومات الأساسية");
            basicInfoTab.BackColor = Color.White;
            basicInfoTab.Padding = new Padding(15);
            CreateBasicInfoTab(basicInfoTab);
            
            // تبويب المعلومات الشخصية
            var personalInfoTab = new TabPage("👨‍👩‍👧‍👦 المعلومات الشخصية");
            personalInfoTab.BackColor = Color.White;
            personalInfoTab.Padding = new Padding(15);
            CreatePersonalInfoTab(personalInfoTab);
            
            // تبويب معلومات العمل
            var workInfoTab = new TabPage("💼 معلومات العمل");
            workInfoTab.BackColor = Color.White;
            workInfoTab.Padding = new Padding(15);
            CreateWorkInfoTab(workInfoTab);
            
            // تبويب المعلومات المالية
            var financialInfoTab = new TabPage("💰 المعلومات المالية");
            financialInfoTab.BackColor = Color.White;
            financialInfoTab.Padding = new Padding(15);
            CreateFinancialInfoTab(financialInfoTab);
            
            // تبويب التعليم والشهادات
            var educationTab = new TabPage("🎓 التعليم والشهادات");
            educationTab.BackColor = Color.White;
            educationTab.Padding = new Padding(15);
            CreateEducationTab(educationTab);
            
            // تبويب معلومات أخرى
            var otherInfoTab = new TabPage("📋 معلومات أخرى");
            otherInfoTab.BackColor = Color.White;
            otherInfoTab.Padding = new Padding(15);
            CreateOtherInfoTab(otherInfoTab);

            tabControl.TabPages.AddRange(new TabPage[] {
                basicInfoTab, personalInfoTab, workInfoTab, 
                financialInfoTab, educationTab, otherInfoTab
            });
            
            this.Controls.Add(tabControl);
        }

        private void CreateBasicInfoTab(TabPage tab)
        {
            var panel = new Panel();
            panel.Dock = DockStyle.Fill;
            panel.AutoScroll = true;
            
            int yPos = 20;
            
            // معلومات الهوية الأساسية - حسب الترتيب المطلوب
            AddSectionTitle(panel, "🆔 البيانات الأساسية", ref yPos);
            AddInfoField(panel, "الرقم:", employee.Id.ToString(), ref yPos);
            AddInfoField(panel, "رقم القيد:", employee.RegistrationNumber, ref yPos);
            AddInfoField(panel, "الرتبة:", employee.Rank, ref yPos);
            AddInfoField(panel, "الاسم واللقب:", employee.FullNameArabic, ref yPos);
            AddInfoField(panel, "NOM:", employee.LastName, ref yPos);
            AddInfoField(panel, "PRENOM:", employee.FirstName, ref yPos);
            
            yPos += 20;
            
            // معلومات الميلاد والهوية
            AddSectionTitle(panel, "🎂 بيانات الميلاد والهوية", ref yPos);
            AddInfoField(panel, "تاريخ الميلاد:", employee.DateOfBirth?.ToString("dd/MM/yyyy"), ref yPos);
            AddInfoField(panel, "مكان الميلاد البلدية بالضبط:", employee.PlaceOfBirth, ref yPos);
            AddInfoField(panel, "الولاية:", employee.Province, ref yPos);
            AddInfoField(panel, "رقم عقد الميلاد:", employee.BirthCertificateNumber, ref yPos);
            AddInfoField(panel, "رقم التعريف الوطني (18 رقم):", employee.NationalId, ref yPos);
            AddInfoField(panel, "الزمرة الدموية:", employee.BloodType, ref yPos);
            
            tab.Controls.Add(panel);
        }

        private void CreatePersonalInfoTab(TabPage tab)
        {
            var panel = new Panel();
            panel.Dock = DockStyle.Fill;
            panel.AutoScroll = true;
            
            int yPos = 20;
            
            // معلومات العائلة - حسب الترتيب المطلوب
            AddSectionTitle(panel, "👨‍👩‍👧‍👦 معلومات الوالدين والعائلة", ref yPos);
            AddInfoField(panel, "إسم الأب:", employee.FatherName, ref yPos);
            AddInfoField(panel, "اسم ولقب الأم:", employee.MotherName, ref yPos);
            AddInfoField(panel, "الحالة العائلية:", employee.MaritalStatus, ref yPos);
            AddInfoField(panel, "عدد الأولاد:", employee.NumberOfChildren?.ToString(), ref yPos);
            
            yPos += 20;
            
            // معلومات الزوجة - تظهر دائماً مع توضيح في حالة العزوبية
            AddSectionTitle(panel, "👰 معلومات الزوجة", ref yPos);
            
            if (!string.IsNullOrEmpty(employee.MaritalStatus) && 
                (employee.MaritalStatus.Contains("متزوج") || employee.MaritalStatus.Contains("married")))
            {
                AddInfoField(panel, "اسم ولقب الزوجة:", employee.SpouseName, ref yPos);
                AddInfoField(panel, "تاريخ ميلاد الزوجة:", employee.SpouseDateOfBirth?.ToString("dd/MM/yyyy"), ref yPos);
                AddInfoField(panel, "مكان ميلاد الزوجة:", employee.SpousePlaceOfBirth, ref yPos);
                AddInfoField(panel, "تاريخ الزواج:", employee.MarriageDate?.ToString("dd/MM/yyyy"), ref yPos);
                AddInfoField(panel, "مهنة الزوجة (عاملة/غير عاملة):", employee.SpouseOccupation, ref yPos);
            }
            else
            {
                AddInfoField(panel, "اسم ولقب الزوجة:", "لا يوجد - أعزب", ref yPos);
                AddInfoField(panel, "تاريخ ميلاد الزوجة:", "لا يوجد", ref yPos);
                AddInfoField(panel, "مكان ميلاد الزوجة:", "لا يوجد", ref yPos);
                AddInfoField(panel, "تاريخ الزواج:", "لا يوجد", ref yPos);
                AddInfoField(panel, "مهنة الزوجة (عاملة/غير عاملة):", "لا يوجد", ref yPos);
            }
            
            yPos += 20;
            
            // معلومات الاتصال
            AddSectionTitle(panel, "📞 معلومات الاتصال والعنوان", ref yPos);
            AddInfoField(panel, "العنوان:", employee.Address, ref yPos);
            AddInfoField(panel, "رقم الهاتف الشخصي:", employee.PersonalPhone, ref yPos);
            AddInfoField(panel, "البريد الالكتروني:", employee.Email, ref yPos);
            
            tab.Controls.Add(panel);
        }

        private void CreateWorkInfoTab(TabPage tab)
        {
            var panel = new Panel();
            panel.Dock = DockStyle.Fill;
            panel.AutoScroll = true;
            
            int yPos = 20;
            
            // معلومات العمل - حسب الترتيب المطلوب
            AddSectionTitle(panel, "💼 معلومات العمل والتوظيف", ref yPos);
            AddInfoField(panel, "مكان العمل:", employee.WorkPlace, ref yPos);
            AddInfoField(panel, "تاريخ التوظيف:", employee.EmploymentDate?.ToString("dd/MM/yyyy"), ref yPos);
            AddInfoField(panel, "تاريخ التعيين في الرتبة:", employee.RankAppointmentDate?.ToString("dd/MM/yyyy"), ref yPos);
            AddInfoField(panel, "الدرجة الحالية:", employee.CurrentGrade, ref yPos);
            AddInfoField(panel, "تاريخ السريان:", employee.EffectiveDate?.ToString("dd/MM/yyyy"), ref yPos);
            AddInfoField(panel, "نظام العمل:", employee.WorkSystem, ref yPos);
            AddInfoField(panel, "الوظيفة:", employee.Position, ref yPos);
            
            yPos += 20;
            
            // حسابات الأقدمية
            AddSectionTitle(panel, "📊 حسابات الأقدمية", ref yPos);
            AddInfoField(panel, "الأقدمية في المهنة:", employee.JobSeniority, ref yPos);
            AddInfoField(panel, "أقدمية الرتبة:", employee.RankSeniority, ref yPos);
            AddInfoField(panel, "العمر الحالي:", $"{employee.Age} سنة", ref yPos);
            
            tab.Controls.Add(panel);
        }

        private void CreateFinancialInfoTab(TabPage tab)
        {
            var panel = new Panel();
            panel.Dock = DockStyle.Fill;
            panel.AutoScroll = true;
            
            int yPos = 20;
            
            // معلومات مالية ومصرفية - حسب الترتيب المطلوب
            AddSectionTitle(panel, "🏦 الحسابات والمعلومات المالية", ref yPos);
            AddInfoField(panel, "رقم الحساب البريدي:", employee.PostalAccountNumber, ref yPos);
            AddInfoField(panel, "رقم الضمان الاجتماعي:", employee.SocialSecurityNumber, ref yPos);
            AddInfoField(panel, "رقم بطاقة التعريف المهنية:", employee.ProfessionalIdNumber, ref yPos);
            AddInfoField(panel, "تاريخ الصدور البطاقة:", employee.IdCardIssueDate?.ToString("dd/MM/yyyy"), ref yPos);
            AddInfoField(panel, "رقم الإنخراط في التعاضدية:", employee.MutualInsuranceNumber, ref yPos);
            
            tab.Controls.Add(panel);
        }

        private void CreateEducationTab(TabPage tab)
        {
            var panel = new Panel();
            panel.Dock = DockStyle.Fill;
            panel.AutoScroll = true;
            
            int yPos = 20;
            
            // معلومات التعليم - حسب الترتيب المطلوب
            AddSectionTitle(panel, "🎓 الشهادات والمؤهلات التعليمية", ref yPos);
            AddInfoField(panel, "نوع الشهادة (آخر/أعلى شهادة متحصل عليها):", employee.CertificateType, ref yPos);
            AddInfoField(panel, "التخصص:", employee.Specialization, ref yPos);
            AddInfoField(panel, "رقم الشهادة:", employee.CertificateNumber, ref yPos);
            AddInfoField(panel, "تاريخ الصدور:", employee.CertificateIssueDate?.ToString("dd/MM/yyyy"), ref yPos);
            AddInfoField(panel, "جهة الاصدار (المؤسسة التعليمية):", employee.IssuingInstitution, ref yPos);
            AddInfoField(panel, "قبل او بعد التوظيف:", employee.BeforeOrAfterEmployment, ref yPos);
            
            tab.Controls.Add(panel);
        }

        private void CreateOtherInfoTab(TabPage tab)
        {
            var panel = new Panel();
            panel.Dock = DockStyle.Fill;
            panel.AutoScroll = true;
            
            int yPos = 20;
            
            // معلومات الخدمة الوطنية - حسب الترتيب المطلوب
            AddSectionTitle(panel, "🎖️ الخدمة الوطنية", ref yPos);
            AddInfoField(panel, "الوضعية اتجاه الخدمة الوطنية:", employee.MilitaryServiceStatus, ref yPos);
            AddInfoField(panel, "تاريخ الصلاحية الى غاية:", employee.ValidityDate?.ToString("dd/MM/yyyy"), ref yPos);
            
            yPos += 20;
            
            // معلومات التحويل
            AddSectionTitle(panel, "🔄 معلومات التحويل", ref yPos);
            AddInfoField(panel, "تاريخ التحويل الداخلي:", employee.InternalTransferDate?.ToString("dd/MM/yyyy"), ref yPos);
            AddInfoField(panel, "تاريخ التحويل من خارج الولاية:", employee.ExternalTransferDate?.ToString("dd/MM/yyyy"), ref yPos);
            
            tab.Controls.Add(panel);
        }

        private void AddSectionTitle(Panel panel, string title, ref int yPos)
        {
            var titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Cairo", 13F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(33, 150, 243);
            titleLabel.Location = new Point(20, yPos);
            titleLabel.Size = new Size(800, 30);
            titleLabel.RightToLeft = RightToLeft.Yes;
            
            var line = new Panel();
            line.BackColor = Color.FromArgb(33, 150, 243);
            line.Size = new Size(800, 2);
            line.Location = new Point(20, yPos + 25);
            
            panel.Controls.AddRange(new Control[] { titleLabel, line });
            yPos += 50;
        }

        private void AddInfoField(Panel panel, string label, string value, ref int yPos)
        {
            var labelControl = new Label();
            labelControl.Text = label;
            labelControl.Font = new Font("Cairo", 11F, FontStyle.Bold);
            labelControl.ForeColor = Color.FromArgb(55, 71, 79);
            labelControl.Location = new Point(20, yPos);
            labelControl.Size = new Size(200, 25);
            labelControl.RightToLeft = RightToLeft.Yes;
            labelControl.TextAlign = ContentAlignment.MiddleRight;
            
            var valueControl = new Label();
            valueControl.Text = string.IsNullOrEmpty(value) ? "غير محدد" : value;
            valueControl.Font = new Font("Cairo", 11F, FontStyle.Regular);
            valueControl.ForeColor = string.IsNullOrEmpty(value) ? Color.Gray : Color.FromArgb(76, 175, 80);
            valueControl.Location = new Point(240, yPos);
            valueControl.Size = new Size(550, 25);
            valueControl.RightToLeft = RightToLeft.Yes;
            valueControl.TextAlign = ContentAlignment.MiddleRight;
            
            panel.Controls.AddRange(new Control[] { labelControl, valueControl });
            yPos += 35;
        }

        private void CreateButtonsPanel()
        {
            var buttonPanel = new Panel();
            buttonPanel.Height = 60;
            buttonPanel.Dock = DockStyle.Bottom;
            buttonPanel.BackColor = Color.FromArgb(245, 245, 245);
            buttonPanel.Padding = new Padding(20);
            
            var closeButton = new Button();
            closeButton.Text = "❌ إغلاق";
            closeButton.Size = new Size(120, 35);
            closeButton.Location = new Point(20, 15);
            closeButton.BackColor = Color.FromArgb(244, 67, 54);
            closeButton.ForeColor = Color.White;
            closeButton.Font = new Font("Cairo", 11F, FontStyle.Bold);
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.RightToLeft = RightToLeft.Yes;
            closeButton.Click += (s, e) => this.Close();
            
            var printButton = new Button();
            printButton.Text = "🖨️ طباعة";
            printButton.Size = new Size(120, 35);
            printButton.Location = new Point(160, 15);
            printButton.BackColor = Color.FromArgb(33, 150, 243);
            printButton.ForeColor = Color.White;
            printButton.Font = new Font("Cairo", 11F, FontStyle.Bold);
            printButton.FlatStyle = FlatStyle.Flat;
            printButton.FlatAppearance.BorderSize = 0;
            printButton.RightToLeft = RightToLeft.Yes;
            printButton.Click += PrintButton_Click;
            
            buttonPanel.Controls.AddRange(new Control[] { closeButton, printButton });
            this.Controls.Add(buttonPanel);
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("🖨️ سيتم تطبيق وظيفة الطباعة قريباً!", "طباعة", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadEmployeeData()
        {
            // البيانات محملة من خلال Constructor
        }
    }
}