@echo off
chcp 65001 >nul
echo.
echo ========================================
echo  🚀 تشغيل سريع - بدون .NET 8          
echo ========================================
echo.

echo 🔨 جاري التجميع...

REM محاولة العثور على C# compiler
set CSC_PATH=""
if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
) else if exist "C:\Windows\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="C:\Windows\Microsoft.NET\Framework\v4.0.30319\csc.exe"
) else (
    echo ❌ لم يتم العثور على C# compiler
    echo 📥 يرجى تثبيت .NET 8 للحصول على النظام الكامل
    pause
    exit /b 1
)

echo ✅ تم العثور على المُجمِع: %CSC_PATH%

REM تجميع البرنامج
%CSC_PATH% /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll /out:QuickEmployeeSystem.exe QuickStart.cs

if %errorlevel% neq 0 (
    echo ❌ فشل التجميع
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح

echo.
echo 🚀 تشغيل البرنامج...
start "نظام الموظفين السريع" QuickEmployeeSystem.exe

if %errorlevel% neq 0 (
    echo ❌ فشل التشغيل
    pause
    exit /b 1
)

echo.
echo 🎊 تم تشغيل النظام بنجاح!
echo.
echo 📖 للحصول على النظام الكامل: ثبّت .NET 8
timeout /t 3 >nul