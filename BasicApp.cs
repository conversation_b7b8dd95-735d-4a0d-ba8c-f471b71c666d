using System;
using System.Windows.Forms;
using System.Drawing;

namespace BasicEmployeeApp
{
    public class BasicForm : Form
    {
        public BasicForm()
        {
            // إعدادات النافذة الأساسية
            this.Text = "نظام إدارة معلومات الموظفين - الإصدار الأساسي";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.White;

            // إنشاء العناصر
            CreateControls();
        }

        private void CreateControls()
        {
            // عنوان رئيسي
            Label titleLabel = new Label();
            titleLabel.Text = "نظام إدارة معلومات الموظفين";
            titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkBlue;
            titleLabel.Location = new Point(250, 30);
            titleLabel.Size = new Size(300, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(titleLabel);

            // رسالة ترحيب
            Label welcomeLabel = new Label();
            welcomeLabel.Text = "مرحباً بك في النظام الأساسي لإدارة بيانات الموظفين";
            welcomeLabel.Font = new Font("Arial", 12);
            welcomeLabel.Location = new Point(200, 80);
            welcomeLabel.Size = new Size(400, 25);
            welcomeLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.Controls.Add(welcomeLabel);

            // معلومات النظام
            Label infoLabel = new Label();
            infoLabel.Text = "هذا الإصدار الأساسي يتضمن:\n" +
                           "• عرض قائمة الموظفين\n" +
                           "• بحث بسيط\n" +
                           "• إضافة موظف جديد\n" +
                           "• عرض تفاصيل الموظف";
            infoLabel.Font = new Font("Arial", 10);
            infoLabel.Location = new Point(50, 130);
            infoLabel.Size = new Size(300, 100);
            this.Controls.Add(infoLabel);

            // قائمة الموظفين (مثال)
            ListBox employeeList = new ListBox();
            employeeList.Location = new Point(50, 250);
            employeeList.Size = new Size(300, 200);
            employeeList.Font = new Font("Arial", 10);
            
            // إضافة بيانات تجريبية
            employeeList.Items.Add("أحمد محمد - مهندس - الرقم: 001");
            employeeList.Items.Add("فاطمة علي - محاسبة - الرقم: 002");
            employeeList.Items.Add("محمد حسن - مدير - الرقم: 003");
            employeeList.Items.Add("سارة أحمد - سكرتيرة - الرقم: 004");
            employeeList.Items.Add("علي محمود - فني - الرقم: 005");
            
            this.Controls.Add(employeeList);

            // مربع البحث
            Label searchLabel = new Label();
            searchLabel.Text = "البحث:";
            searchLabel.Location = new Point(400, 250);
            searchLabel.Size = new Size(50, 20);
            this.Controls.Add(searchLabel);

            TextBox searchBox = new TextBox();
            searchBox.Location = new Point(460, 250);
            searchBox.Size = new Size(200, 25);
            searchBox.Font = new Font("Arial", 10);
            this.Controls.Add(searchBox);

            // زر البحث
            Button searchButton = new Button();
            searchButton.Text = "بحث";
            searchButton.Location = new Point(680, 250);
            searchButton.Size = new Size(80, 25);
            searchButton.Click += (s, e) => {
                string searchText = searchBox.Text;
                if (string.IsNullOrEmpty(searchText))
                {
                    MessageBox.Show("يرجى إدخال نص للبحث", "تنبيه");
                }
                else
                {
                    MessageBox.Show($"البحث عن: {searchText}\n(هذه وظيفة تجريبية)", "نتائج البحث");
                }
            };
            this.Controls.Add(searchButton);

            // زر إضافة موظف
            Button addButton = new Button();
            addButton.Text = "إضافة موظف";
            addButton.Location = new Point(400, 300);
            addButton.Size = new Size(100, 30);
            addButton.BackColor = Color.LightGreen;
            addButton.Click += (s, e) => {
                MessageBox.Show("سيتم فتح نافذة إضافة موظف جديد\n(قريباً)", "إضافة موظف");
            };
            this.Controls.Add(addButton);

            // زر عرض التفاصيل
            Button detailsButton = new Button();
            detailsButton.Text = "عرض التفاصيل";
            detailsButton.Location = new Point(520, 300);
            detailsButton.Size = new Size(100, 30);
            detailsButton.BackColor = Color.LightBlue;
            detailsButton.Click += (s, e) => {
                if (employeeList.SelectedItem != null)
                {
                    string selectedEmployee = employeeList.SelectedItem.ToString();
                    MessageBox.Show($"تفاصيل الموظف:\n{selectedEmployee}\n\n" +
                                  "العمر: 30 سنة\n" +
                                  "تاريخ التوظيف: 2020/01/01\n" +
                                  "الراتب: 5000 ريال\n" +
                                  "(بيانات تجريبية)", "تفاصيل الموظف");
                }
                else
                {
                    MessageBox.Show("يرجى اختيار موظف من القائمة", "تنبيه");
                }
            };
            this.Controls.Add(detailsButton);

            // زر طباعة
            Button printButton = new Button();
            printButton.Text = "طباعة";
            printButton.Location = new Point(640, 300);
            printButton.Size = new Size(80, 30);
            printButton.BackColor = Color.LightYellow;
            printButton.Click += (s, e) => {
                MessageBox.Show("سيتم طباعة بطاقة الموظف\n(قريباً)", "طباعة");
            };
            this.Controls.Add(printButton);

            // معلومات الحالة
            Label statusLabel = new Label();
            statusLabel.Text = "الحالة: النظام جاهز للاستخدام";
            statusLabel.Location = new Point(50, 500);
            statusLabel.Size = new Size(300, 20);
            statusLabel.ForeColor = Color.Green;
            this.Controls.Add(statusLabel);

            // معلومات الإصدار
            Label versionLabel = new Label();
            versionLabel.Text = "الإصدار الأساسي 1.0 - تم التطوير بنجاح";
            versionLabel.Location = new Point(400, 500);
            versionLabel.Size = new Size(300, 20);
            versionLabel.ForeColor = Color.Gray;
            versionLabel.Font = new Font("Arial", 8);
            this.Controls.Add(versionLabel);
        }
    }

    // البرنامج الرئيسي
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // رسالة بدء التشغيل
                MessageBox.Show("مرحباً! جاري تشغيل نظام إدارة معلومات الموظفين...", 
                              "بدء التشغيل", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // تشغيل التطبيق
                Application.Run(new BasicForm());
                
                // رسالة الإغلاق
                MessageBox.Show("شكراً لاستخدام النظام!", "إغلاق البرنامج", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تشغيل البرنامج:\n{ex.Message}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
