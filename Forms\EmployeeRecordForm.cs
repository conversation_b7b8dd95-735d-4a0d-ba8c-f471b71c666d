using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using EmployeeManagementSystem.Models;
using EmployeeManagementSystem.Services;

namespace EmployeeManagementSystem.Forms
{
    public partial class EmployeeRecordForm : Form
    {
        private User employee;
        private Panel mainPanel;
        
        public EmployeeRecordForm(User user)
        {
            employee = user;
            InitializeComponent();
            LoadEmployeeRecord();
        }

        private void InitializeComponent()
        {
            this.Text = $"📋 سجل الموظف الكامل - {employee.FullNameArabic}";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(250, 250, 250);
            this.Font = new Font("Cairo", 12F, FontStyle.Regular);

            CreateHeaderPanel();
            CreateMainPanel();
            CreateButtonsPanel();
        }

        private void CreateHeaderPanel()
        {
            var headerPanel = new Panel();
            headerPanel.Height = 80;
            headerPanel.Dock = DockStyle.Top;
            headerPanel.BackColor = Color.FromArgb(55, 71, 79);
            
            var titleLabel = new Label();
            titleLabel.Text = $"📋 السجل الكامل للموظف: {employee.FullNameArabic ?? "موظف غير مسمى"}";
            titleLabel.Font = new Font("Cairo", 18F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(20, 15);
            titleLabel.AutoSize = true;
            titleLabel.RightToLeft = RightToLeft.Yes;
            
            var subtitleLabel = new Label();
            subtitleLabel.Text = $"🆔 رقم القيد: {employee.RegistrationNumber} | 🎖️ الرتبة: {employee.Rank ?? "غير محدد"}";
            subtitleLabel.Font = new Font("Cairo", 12F, FontStyle.Regular);
            subtitleLabel.ForeColor = Color.FromArgb(200, 200, 200);
            subtitleLabel.Location = new Point(20, 50);
            subtitleLabel.AutoSize = true;
            subtitleLabel.RightToLeft = RightToLeft.Yes;
            
            headerPanel.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
            this.Controls.Add(headerPanel);
        }

        private void CreateMainPanel()
        {
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.AutoScroll = true;
            mainPanel.BackColor = Color.White;
            mainPanel.Padding = new Padding(30, 20, 30, 20);
            
            this.Controls.Add(mainPanel);
        }

        private void LoadEmployeeRecord()
        {
            int yPos = 30;
            int leftColumnX = 50;
            int rightColumnX = 750;
            int fieldHeight = 40;
            int sectionSpacing = 50;

            // عنوان السجل
            var recordTitle = CreateSectionHeader("📋 بيانات الموظف الكاملة", yPos);
            mainPanel.Controls.Add(recordTitle);
            yPos += 50;

            // العمود الأيسر والأيمن معاً
            
            // 1. البيانات الأساسية
            yPos = AddSectionTitle("🆔 البيانات الأساسية", yPos);
            yPos = AddTwoColumnField("الرقم:", employee.Id.ToString(), "رقم القيد:", employee.RegistrationNumber, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("الرتبة:", employee.Rank, "الاسم واللقب:", employee.FullNameArabic, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("NOM:", employee.LastName, "PRENOM:", employee.FirstName, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos += sectionSpacing;

            // 2. بيانات الميلاد والهوية
            yPos = AddSectionTitle("🎂 بيانات الميلاد والهوية", yPos);
            yPos = AddTwoColumnField("تاريخ الميلاد:", employee.DateOfBirth?.ToString("dd/MM/yyyy"), "مكان الميلاد البلدية بالضبط:", employee.PlaceOfBirth, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("الولاية:", employee.Province, "رقم عقد الميلاد:", employee.BirthCertificateNumber, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("رقم التعريف الوطني (18 رقم):", employee.NationalId, "الزمرة الدموية:", employee.BloodType, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos += sectionSpacing;

            // 3. معلومات العائلة
            yPos = AddSectionTitle("👨‍👩‍👧‍👦 معلومات الوالدين والعائلة", yPos);
            yPos = AddTwoColumnField("إسم الأب:", employee.FatherName, "اسم ولقب الأم:", employee.MotherName, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("الحالة العائلية:", employee.MaritalStatus, "عدد الأولاد:", employee.NumberOfChildren?.ToString(), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos += sectionSpacing;

            // 4. معلومات الزوجة
            yPos = AddSectionTitle("👰 معلومات الزوجة", yPos);
            string spouseInfo = GetSpouseInfo();
            yPos = AddTwoColumnField("اسم ولقب الزوجة:", GetSpouseValue(employee.SpouseName), "تاريخ ميلاد الزوجة:", GetSpouseValue(employee.SpouseDateOfBirth?.ToString("dd/MM/yyyy")), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("مكان ميلاد الزوجة:", GetSpouseValue(employee.SpousePlaceOfBirth), "تاريخ الزواج:", GetSpouseValue(employee.MarriageDate?.ToString("dd/MM/yyyy")), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddSingleColumnField("مهنة الزوجة (عاملة/غير عاملة):", GetSpouseValue(employee.SpouseOccupation), leftColumnX, yPos, fieldHeight, 1100);
            yPos += sectionSpacing;

            // 5. معلومات الاتصال
            yPos = AddSectionTitle("📞 معلومات الاتصال والعنوان", yPos);
            yPos = AddSingleColumnField("العنوان:", employee.Address, leftColumnX, yPos, fieldHeight, 1100);
            yPos = AddTwoColumnField("رقم الهاتف الشخصي:", employee.PersonalPhone, "البريد الالكتروني:", employee.Email, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos += sectionSpacing;

            // 6. معلومات العمل
            yPos = AddSectionTitle("💼 معلومات العمل والتوظيف", yPos);
            yPos = AddTwoColumnField("مكان العمل:", employee.WorkPlace, "تاريخ التوظيف:", employee.EmploymentDate?.ToString("dd/MM/yyyy"), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("تاريخ التعيين في الرتبة:", employee.RankAppointmentDate?.ToString("dd/MM/yyyy"), "الدرجة الحالية:", employee.CurrentGrade, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("تاريخ السريان:", employee.EffectiveDate?.ToString("dd/MM/yyyy"), "نظام العمل:", employee.WorkSystem, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddSingleColumnField("الوظيفة:", employee.Position, leftColumnX, yPos, fieldHeight, 1100);
            yPos += sectionSpacing;

            // 7. المعلومات المالية
            yPos = AddSectionTitle("🏦 الحسابات والمعلومات المالية", yPos);
            yPos = AddTwoColumnField("رقم الحساب البريدي:", employee.PostalAccountNumber, "رقم الضمان الاجتماعي:", employee.SocialSecurityNumber, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("رقم بطاقة التعريف المهنية:", employee.ProfessionalIdNumber, "تاريخ الصدور البطاقة:", employee.IdCardIssueDate?.ToString("dd/MM/yyyy"), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddSingleColumnField("رقم الإنخراط في التعاضدية:", employee.MutualInsuranceNumber, leftColumnX, yPos, fieldHeight, 1100);
            yPos += sectionSpacing;

            // 8. التعليم والشهادات
            yPos = AddSectionTitle("🎓 الشهادات والمؤهلات التعليمية", yPos);
            yPos = AddTwoColumnField("نوع الشهادة (آخر/أعلى شهادة):", employee.CertificateType, "التخصص:", employee.Specialization, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("رقم الشهادة:", employee.CertificateNumber, "تاريخ الصدور:", employee.CertificateIssueDate?.ToString("dd/MM/yyyy"), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("جهة الاصدار (المؤسسة التعليمية):", employee.IssuingInstitution, "قبل او بعد التوظيف:", employee.BeforeOrAfterEmployment, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos += sectionSpacing;

            // 9. معلومات أخرى
            yPos = AddSectionTitle("🎖️ الخدمة الوطنية والتحويلات", yPos);
            yPos = AddTwoColumnField("الوضعية اتجاه الخدمة الوطنية:", employee.MilitaryServiceStatus, "تاريخ الصلاحية الى غاية:", employee.ValidityDate?.ToString("dd/MM/yyyy"), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddTwoColumnField("تاريخ التحويل الداخلي:", employee.InternalTransferDate?.ToString("dd/MM/yyyy"), "تاريخ التحويل من خارج الولاية:", employee.ExternalTransferDate?.ToString("dd/MM/yyyy"), leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos += sectionSpacing;

            // 10. حسابات تلقائية
            yPos = AddSectionTitle("📊 الحسابات التلقائية", yPos);
            yPos = AddTwoColumnField("العمر الحالي:", $"{employee.Age} سنة", "الأقدمية في المهنة:", employee.JobSeniority, leftColumnX, rightColumnX, yPos, fieldHeight);
            yPos = AddSingleColumnField("أقدمية الرتبة:", employee.RankSeniority, leftColumnX, yPos, fieldHeight, 1100);

            // إضافة مساحة إضافية في النهاية
            yPos += 50;
        }

        private string GetSpouseInfo()
        {
            return (!string.IsNullOrEmpty(employee.MaritalStatus) && 
                   (employee.MaritalStatus.Contains("متزوج") || employee.MaritalStatus.Contains("married"))) 
                   ? "متزوج" : "أعزب";
        }

        private string GetSpouseValue(string value)
        {
            if (GetSpouseInfo() == "أعزب")
                return "لا يوجد - أعزب";
            
            return string.IsNullOrEmpty(value) ? "غير محدد" : value;
        }

        private Label CreateSectionHeader(string title, int yPos)
        {
            var header = new Label();
            header.Text = title;
            header.Font = new Font("Cairo", 16F, FontStyle.Bold);
            header.ForeColor = Color.FromArgb(33, 150, 243);
            header.Location = new Point(50, yPos);
            header.Size = new Size(1000, 35);
            header.RightToLeft = RightToLeft.Yes;
            header.TextAlign = ContentAlignment.MiddleRight;
            return header;
        }

        private int AddSectionTitle(string title, int yPos)
        {
            var titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("Cairo", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(33, 150, 243);
            titleLabel.Location = new Point(50, yPos);
            titleLabel.Size = new Size(1100, 30);
            titleLabel.RightToLeft = RightToLeft.Yes;
            titleLabel.TextAlign = ContentAlignment.MiddleRight;
            
            var line = new Panel();
            line.BackColor = Color.FromArgb(33, 150, 243);
            line.Size = new Size(1100, 2);
            line.Location = new Point(50, yPos + 25);
            
            mainPanel.Controls.AddRange(new Control[] { titleLabel, line });
            return yPos + 40;
        }

        private int AddTwoColumnField(string label1, string value1, string label2, string value2, int leftX, int rightX, int yPos, int height)
        {
            // العمود الأيسر
            var leftLabel = CreateFieldLabel(label1, leftX, yPos, 180);
            var leftValue = CreateFieldValue(value1, leftX + 190, yPos, 400);
            
            // العمود الأيمن
            var rightLabel = CreateFieldLabel(label2, rightX, yPos, 180);
            var rightValue = CreateFieldValue(value2, rightX + 190, yPos, 400);
            
            mainPanel.Controls.AddRange(new Control[] { leftLabel, leftValue, rightLabel, rightValue });
            return yPos + height;
        }

        private int AddSingleColumnField(string label, string value, int xPos, int yPos, int height, int totalWidth)
        {
            var fieldLabel = CreateFieldLabel(label, xPos, yPos, 180);
            var fieldValue = CreateFieldValue(value, xPos + 190, yPos, totalWidth - 190);
            
            mainPanel.Controls.AddRange(new Control[] { fieldLabel, fieldValue });
            return yPos + height;
        }

        private Label CreateFieldLabel(string text, int x, int y, int width)
        {
            var label = new Label();
            label.Text = text;
            label.Font = new Font("Cairo", 13F, FontStyle.Bold);
            label.ForeColor = Color.FromArgb(55, 71, 79);
            label.Location = new Point(x, y);
            label.Size = new Size(width, 30);
            label.RightToLeft = RightToLeft.Yes;
            label.TextAlign = ContentAlignment.MiddleRight;
            return label;
        }

        private TextBox CreateFieldValue(string text, int x, int y, int width)
        {
            var textBox = new TextBox();
            textBox.Text = string.IsNullOrEmpty(text) ? "غير محدد" : text;
            textBox.Font = new Font("Cairo", 13F, FontStyle.Regular);
            textBox.ForeColor = string.IsNullOrEmpty(text) ? Color.Gray : Color.FromArgb(33, 33, 33);
            textBox.BackColor = Color.White;
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.Location = new Point(x, y);
            textBox.Size = new Size(width, 30);
            textBox.RightToLeft = RightToLeft.Yes;
            textBox.TextAlign = HorizontalAlignment.Right;
            textBox.ReadOnly = true;
            textBox.TabStop = false;
            textBox.Multiline = false;
            return textBox;
        }

        private void CreateButtonsPanel()
        {
            var buttonPanel = new Panel();
            buttonPanel.Height = 60;
            buttonPanel.Dock = DockStyle.Bottom;
            buttonPanel.BackColor = Color.FromArgb(245, 245, 245);
            buttonPanel.Padding = new Padding(20);
            
            var closeButton = new Button();
            closeButton.Text = "❌ إغلاق";
            closeButton.Size = new Size(120, 35);
            closeButton.Location = new Point(20, 15);
            closeButton.BackColor = Color.FromArgb(244, 67, 54);
            closeButton.ForeColor = Color.White;
            closeButton.Font = new Font("Cairo", 11F, FontStyle.Bold);
            closeButton.FlatStyle = FlatStyle.Flat;
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.RightToLeft = RightToLeft.Yes;
            closeButton.Click += (s, e) => this.Close();
            
            var printButton = new Button();
            printButton.Text = "🖨️ طباعة السجل";
            printButton.Size = new Size(150, 35);
            printButton.Location = new Point(160, 15);
            printButton.BackColor = Color.FromArgb(33, 150, 243);
            printButton.ForeColor = Color.White;
            printButton.Font = new Font("Cairo", 11F, FontStyle.Bold);
            printButton.FlatStyle = FlatStyle.Flat;
            printButton.FlatAppearance.BorderSize = 0;
            printButton.RightToLeft = RightToLeft.Yes;
            printButton.Click += PrintButton_Click;
            
            var exportButton = new Button();
            exportButton.Text = "📄 تصدير PDF";
            exportButton.Size = new Size(130, 35);
            exportButton.Location = new Point(330, 15);
            exportButton.BackColor = Color.FromArgb(76, 175, 80);
            exportButton.ForeColor = Color.White;
            exportButton.Font = new Font("Cairo", 11F, FontStyle.Bold);
            exportButton.FlatStyle = FlatStyle.Flat;
            exportButton.FlatAppearance.BorderSize = 0;
            exportButton.RightToLeft = RightToLeft.Yes;
            exportButton.Click += ExportButton_Click;
            
            buttonPanel.Controls.AddRange(new Control[] { closeButton, printButton, exportButton });
            this.Controls.Add(buttonPanel);
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء وثيقة طباعة للسجل الكامل
                var printDialog = new PrintDialog();
                var printDocument = new System.Drawing.Printing.PrintDocument();
                
                printDocument.PrintPage += (s, args) => {
                    var graphics = args.Graphics;
                    var font = new Font("Cairo", 10F, FontStyle.Regular);
                    var titleFont = new Font("Cairo", 14F, FontStyle.Bold);
                    var brush = Brushes.Black;
                    
                    int yPos = 50;
                    int margin = 50;
                    
                    // طباعة رأس الصفحة الرسمي
                    graphics.DrawString("الجمهورية الجزائرية الديمقراطية الشعبية", titleFont, brush, margin, yPos);
                    yPos += 30;
                    graphics.DrawString("وزارة الداخلية والجماعات المحلية والتهيئة العمرانية", font, brush, margin, yPos);
                    yPos += 25;
                    graphics.DrawString("المديرية العامة للحماية المدنية", font, brush, margin, yPos);
                    yPos += 25;
                    graphics.DrawString("مديرية الحماية المدنية لولاية الجلفة", font, brush, margin, yPos);
                    yPos += 25;
                    graphics.DrawString("الوحدة الرئيسية بالجلفة", font, brush, margin, yPos);
                    yPos += 40;
                    
                    graphics.DrawString($"الرقم: و ر ح و ج/ {DateTime.Now.Year}", font, brush, margin, yPos);
                    yPos += 30;
                    
                    graphics.DrawString("📋 السجل الكامل للموظف", titleFont, brush, margin, yPos);
                    yPos += 40;
                    
                    // طباعة معلومات الموظف الأساسية
                    graphics.DrawString($"الاسم: {employee.FullNameArabic}", font, brush, margin, yPos);
                    yPos += 25;
                    graphics.DrawString($"رقم القيد: {employee.RegistrationNumber}", font, brush, margin, yPos);
                    yPos += 25;
                    graphics.DrawString($"الرتبة: {employee.Rank}", font, brush, margin, yPos);
                    yPos += 25;
                    graphics.DrawString($"رقم التعريف الوطني: {employee.NationalId}", font, brush, margin, yPos);
                    yPos += 25;
                    graphics.DrawString($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}", font, brush, margin, yPos);
                };
                
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    printDocument.Print();
                    MessageBox.Show("✅ تمت طباعة السجل بنجاح!", "طباعة", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportButton_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog();
                saveDialog.Filter = "ملفات Excel|*.xlsx|ملفات PDF|*.pdf";
                saveDialog.FileName = $"سجل_{employee.FullNameArabic}_{DateTime.Now:yyyy-MM-dd}.xlsx";
                
                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // تصدير إلى Excel
                    if (saveDialog.FileName.EndsWith(".xlsx"))
                    {
                        ExportToExcel(saveDialog.FileName);
                        MessageBox.Show("✅ تم تصدير السجل إلى Excel بنجاح!", "تصدير", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("📄 تصدير PDF سيتم تطبيقه قريباً!", "تصدير", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void ExportToExcel(string fileName)
        {
            // تصدير بيانات الموظف إلى Excel
            try
            {
                var exportService = new ExportService();
                var employeeList = new List<User> { employee };
                exportService.ExportToExcel(employeeList, fileName);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تصدير البيانات: {ex.Message}");
            }
        }
    }
}