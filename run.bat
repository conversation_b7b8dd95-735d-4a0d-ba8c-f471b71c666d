@echo off
echo ========================================
echo    نظام إدارة معلومات الموظفين
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...

:: التحقق من وجود .NET
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET غير مثبت على النظام
    echo يرجى تثبيت .NET 8.0 من الرابط التالي:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo تم العثور على .NET
echo.

echo جاري استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    pause
    exit /b 1
)

echo جاري بناء المشروع...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo.
echo تم بناء المشروع بنجاح!
echo جاري تشغيل التطبيق...
echo.

dotnet run --configuration Release

echo.
echo تم إغلاق التطبيق
pause
