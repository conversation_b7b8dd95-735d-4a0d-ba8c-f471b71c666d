@echo off
echo ========================================
echo    بناء نظام إدارة معلومات الموظفين
echo ========================================
echo.

set OUTPUT_DIR=Release
set APP_NAME=EmployeeManagementSystem

echo جاري تنظيف المجلدات السابقة...
if exist %OUTPUT_DIR% rmdir /s /q %OUTPUT_DIR%
mkdir %OUTPUT_DIR%

echo.
echo جاري بناء التطبيق للنشر...
dotnet publish -c Release -r win-x64 --self-contained true -o %OUTPUT_DIR%\%APP_NAME%

if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo جاري نسخ الملفات الإضافية...
copy README.md %OUTPUT_DIR%\%APP_NAME%\
copy DEVELOPER_GUIDE.md %OUTPUT_DIR%\%APP_NAME%\

echo.
echo جاري إنشاء ملف تشغيل...
echo @echo off > %OUTPUT_DIR%\%APP_NAME%\start.bat
echo echo جاري تشغيل نظام إدارة معلومات الموظفين... >> %OUTPUT_DIR%\%APP_NAME%\start.bat
echo %APP_NAME%.exe >> %OUTPUT_DIR%\%APP_NAME%\start.bat

echo.
echo جاري إنشاء ملف ZIP...
powershell Compress-Archive -Path "%OUTPUT_DIR%\%APP_NAME%\*" -DestinationPath "%OUTPUT_DIR%\%APP_NAME%_v1.0.zip" -Force

echo.
echo ========================================
echo تم بناء التطبيق بنجاح!
echo ========================================
echo.
echo مجلد الإخراج: %OUTPUT_DIR%\%APP_NAME%
echo ملف ZIP: %OUTPUT_DIR%\%APP_NAME%_v1.0.zip
echo.
echo يمكنك الآن توزيع التطبيق عبر نسخ المجلد أو ملف ZIP
echo.
pause
