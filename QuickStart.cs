using System;
using System.Drawing;
using System.Windows.Forms;

namespace QuickEmployeeSystem
{
    public partial class QuickMainForm : Form
    {
        public QuickMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد النموذج الرئيسي
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Text = "🏢 نظام إدارة معلومات الموظفين - مديرية الحماية المدنية لولاية الجلفة";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            try
            {
                this.Font = new Font("Cairo", 12F, FontStyle.Regular);
            }
            catch
            {
                this.Font = new Font("Tahoma", 12F, FontStyle.Regular);
            }

            // إنشاء لوحة العنوان
            Panel headerPanel = new Panel();
            headerPanel.Dock = DockStyle.Top;
            headerPanel.Height = 120;
            headerPanel.BackColor = Color.FromArgb(33, 150, 243);
            
            Label titleLabel = new Label();
            titleLabel.Text = "🏢 نظام إدارة معلومات الموظفين";
            titleLabel.Font = new Font(this.Font.FontFamily, 24F, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.AutoSize = true;
            titleLabel.Location = new Point(400, 20);
            headerPanel.Controls.Add(titleLabel);

            Label subtitleLabel = new Label();
            subtitleLabel.Text = "🎖️ مديرية الحماية المدنية لولاية الجلفة";
            subtitleLabel.Font = new Font(this.Font.FontFamily, 16F, FontStyle.Regular);
            subtitleLabel.ForeColor = Color.White;
            subtitleLabel.AutoSize = true;
            subtitleLabel.Location = new Point(450, 70);
            headerPanel.Controls.Add(subtitleLabel);

            // لوحة المحتوى
            Panel contentPanel = new Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.BackColor = Color.White;
            contentPanel.Padding = new Padding(50);

            // رسالة ترحيب
            Label welcomeLabel = new Label();
            welcomeLabel.Text = @"🎉 مرحباً بك في نظام إدارة الموظفين الجديد!

✅ تم إنجاز جميع التحديثات المطلوبة:
• حذف الجدول وإضافة نظام إدارة الموظفين
• أزرار الإدارة: إضافة، تعديل، حذف، عرض السجل
• تحسين السجل الكامل والقراءة العربية
• تفعيل الطباعة مع الرأس الرسمي
• تصدير واستيراد Excel

⚠️ لتشغيل النظام الكامل:
1. ثبّت .NET 8 من install-dotnet.bat
2. أعد تشغيل الكمبيوتر
3. استخدم start-arabic-cairo-updated.bat

📖 للمساعدة: اقرأ ملف تعليمات_التشغيل.md";
            
            welcomeLabel.Font = new Font(this.Font.FontFamily, 14F, FontStyle.Regular);
            welcomeLabel.ForeColor = Color.FromArgb(55, 71, 79);
            welcomeLabel.Dock = DockStyle.Top;
            welcomeLabel.Height = 400;
            welcomeLabel.TextAlign = ContentAlignment.TopRight;
            contentPanel.Controls.Add(welcomeLabel);

            // زر التثبيت
            Button installButton = new Button();
            installButton.Text = "📥 تثبيت .NET 8 للحصول على النظام الكامل";
            installButton.Size = new Size(400, 50);
            installButton.Location = new Point(400, 450);
            installButton.BackColor = Color.FromArgb(76, 175, 80);
            installButton.ForeColor = Color.White;
            installButton.FlatStyle = FlatStyle.Flat;
            installButton.Font = new Font(this.Font.FontFamily, 12F, FontStyle.Bold);
            installButton.Click += (s, e) => {
                try
                {
                    System.Diagnostics.Process.Start("install-dotnet.bat");
                }
                catch
                {
                    MessageBox.Show("يرجى تشغيل ملف install-dotnet.bat يدوياً", "تنبيه");
                }
            };
            contentPanel.Controls.Add(installButton);

            // شريط الحالة
            StatusStrip statusStrip = new StatusStrip();
            statusStrip.BackColor = Color.FromArgb(96, 125, 139);
            ToolStripStatusLabel statusLabel = new ToolStripStatusLabel();
            statusLabel.Text = "✅ النظام جاهز - يحتاج .NET 8 للتشغيل الكامل";
            statusLabel.ForeColor = Color.White;
            statusStrip.Items.Add(statusLabel);

            // إضافة العناصر للنموذج
            this.Controls.Add(contentPanel);
            this.Controls.Add(headerPanel);
            this.Controls.Add(statusStrip);

            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }

    internal static class QuickProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new QuickMainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ: " + ex.Message, "خطأ في التشغيل", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}