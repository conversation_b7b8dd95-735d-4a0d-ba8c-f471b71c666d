@echo off
chcp 65001 >nul
title نظام إدارة معلومات الموظفين - مديرية الحماية المدنية لولاية الجلفة

echo.
echo ========================================
echo  🏢 نظام إدارة معلومات الموظفين        
echo  🎖️ مديرية الحماية المدنية لولاية الجلفة
echo ========================================
echo.

echo 🔍 فحص النظام...

REM التحقق من وجود .NET 8
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET 8 موجود - تشغيل النظام الكامل
    echo.
    echo 🚀 تشغيل النظام الكامل مع جميع المميزات...
    call start-arabic-cairo-updated.bat
    exit /b 0
)

echo ⚠️  .NET 8 غير موجود - تشغيل النسخة السريعة
echo.

REM تشغيل النسخة السريعة
echo 🔨 بناء وتشغيل النسخة السريعة...

REM التحقق من وجود الملف المُجمع مسبقاً
if exist "QuickEmployeeSystem.exe" (
    echo ✅ الملف التنفيذي موجود - تشغيل مباشر
    start "نظام الموظفين" QuickEmployeeSystem.exe
    echo.
    echo 🎊 تم تشغيل النظام بنجاح!
    echo.
    echo ℹ️  هذه نسخة مبسطة للمعاينة
    echo 📥 للحصول على النظام الكامل: انقر install-dotnet.bat
    goto :show_info
)

REM إذا لم يكن موجود، جرب البناء
call run-quick.bat
if %errorlevel% equ 0 (
    goto :show_info
)

echo.
echo ❌ لم يتم العثور على أي طريقة للتشغيل
echo.
echo 💡 الحلول المتاحة:
echo    1️⃣ تثبيت .NET 8: انقر install-dotnet.bat
echo    2️⃣ قراءة التعليمات: تعليمات_التشغيل.md
echo    3️⃣ دليل الاستخدام: دليل_التشغيل_السريع.md
echo.
pause
exit /b 1

:show_info
echo.
echo ========================================
echo  📋 معلومات النظام                    
echo ========================================
echo.
echo ✅ الوظائف المتاحة في النسخة الحالية:
echo    • عرض معلومات النظام
echo    • إرشادات التثبيت
echo    • روابط المساعدة
echo.
echo 🌟 الوظائف الكاملة (تحتاج .NET 8):
echo    • إدارة قاعدة بيانات الموظفين
echo    • بحث ذكي وسريع
echo    • السجل الكامل للموظفين
echo    • طباعة البطاقات الرسمية
echo    • استيراد وتصدير Excel
echo    • واجهة عربية متكاملة
echo.
echo 📥 لتثبيت .NET 8: انقر نقرة مزدوجة على install-dotnet.bat
echo 📖 للمساعدة: اقرأ ملف تعليمات_التشغيل.md
echo.
timeout /t 10 >nul
echo انتهت المهمة بنجاح!