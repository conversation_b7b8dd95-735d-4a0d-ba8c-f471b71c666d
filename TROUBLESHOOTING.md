# دليل استكشاف الأخطاء وإصلاحها

## المشاكل الشائعة وحلولها

### 1. البرنامج لا يعمل أو لا يبدأ

#### أ. التحقق من متطلبات النظام
```bash
# تحقق من وجود .NET
dotnet --version
```

**إذا ظهر خطأ "dotnet is not recognized":**
- قم بتثبيت .NET 6.0 أو أحدث من: https://dotnet.microsoft.com/download
- أعد تشغيل الكمبيوتر بعد التثبيت

#### ب. اختبار التطبيق الأساسي
```bash
# شغل تطبيق الاختبار أولاً
test-app.bat
```

إذا عمل تطبيق الاختبار، فالمشكلة في التطبيق الرئيسي.

#### ج. تشغيل النسخة المبسطة
```bash
# شغل النسخة المبسطة
run-simple.bat
```

### 2. خطأ في بناء المشروع

#### أ. تنظيف المشروع
```bash
dotnet clean
dotnet restore
dotnet build
```

#### ب. حذف مجلدات التخزين المؤقت
```bash
rmdir /s /q bin
rmdir /s /q obj
dotnet restore
```

### 3. خطأ في قاعدة البيانات

#### أ. إعادة إنشاء قاعدة البيانات
- احذف ملف `EmployeeDatabase.sqlite` إن وجد
- شغل التطبيق مرة أخرى (سيتم إنشاء قاعدة بيانات جديدة)

#### ب. التحقق من صلاحيات الملفات
- تأكد من أن المجلد قابل للكتابة
- شغل التطبيق كمدير إذا لزم الأمر

### 4. مشاكل استيراد Excel

#### أ. تنسيق الملف
- تأكد من أن الملف بصيغة .xlsx أو .xls
- تأكد من وجود البيانات في الورقة الأولى
- تأكد من ترتيب الأعمدة الصحيح

#### ب. ترتيب الأعمدة المطلوب
```
العمود 1: الرقم
العمود 2: رقم القيد  
العمود 3: الرتبة
العمود 4: الاسم واللقب
العمود 5: NOM
العمود 6: PRENOM
العمود 7: تاريخ الميلاد
... إلخ (46 عمود)
```

### 5. مشاكل الطباعة

#### أ. تعريف الطابعة
- تأكد من تثبيت طابعة على النظام
- جرب طباعة ملف آخر للتأكد من عمل الطابعة

#### ب. مشاكل QR Code
- تأكد من تثبيت مكتبة QRCoder
- جرب إعادة بناء المشروع

### 6. مشاكل التصدير

#### أ. صلاحيات الحفظ
- تأكد من صلاحيات الكتابة في المجلد المختار
- جرب الحفظ في مجلد آخر

#### ب. ملف Excel مفتوح
- أغلق ملف Excel إذا كان مفتوحاً
- جرب اسم ملف مختلف

## خطوات التشخيص المتقدم

### 1. تشغيل من سطر الأوامر
```bash
# للحصول على رسائل خطأ مفصلة
dotnet run --project EmployeeManagementSystem.csproj
```

### 2. فحص ملفات السجل
- ابحث عن ملفات .log في مجلد التطبيق
- افحص رسائل الخطأ في Event Viewer

### 3. اختبار المكونات منفردة

#### أ. اختبار قاعدة البيانات
```csharp
// في C# Interactive أو تطبيق console
using EmployeeManagementSystem.Data;
var db = new DatabaseManager();
var users = db.GetAllUsers();
Console.WriteLine($"عدد المستخدمين: {users.Count}");
```

#### ب. اختبار استيراد Excel
```csharp
using EmployeeManagementSystem.Services;
var importer = new ExcelImportService(db);
var result = importer.ImportFromExcel("test.xlsx");
Console.WriteLine($"نجح: {result.SuccessCount}, فشل: {result.ErrorCount}");
```

## إصدارات مختلفة للاختبار

### 1. تطبيق الاختبار الأساسي
```bash
test-app.bat
```
- يختبر Windows Forms فقط
- لا يحتاج مكتبات خارجية

### 2. النسخة المبسطة
```bash
run-simple.bat
```
- تحتوي على الوظائف الأساسية
- أقل تعقيداً من النسخة الكاملة

### 3. النسخة الكاملة
```bash
run.bat
```
- جميع الميزات
- تحتاج جميع المكتبات

## معلومات النظام المطلوبة

### الحد الأدنى
- Windows 10 أو أحدث
- .NET 6.0 Runtime
- 2 GB RAM
- 100 MB مساحة تخزين

### المستحسن
- Windows 11
- .NET 8.0 Runtime  
- 4 GB RAM
- 500 MB مساحة تخزين
- طابعة مثبتة

## الحصول على المساعدة

### 1. معلومات مفيدة للدعم
عند طلب المساعدة، قدم المعلومات التالية:
- إصدار Windows
- إصدار .NET (`dotnet --version`)
- رسالة الخطأ الكاملة
- الخطوات التي أدت للخطأ

### 2. ملفات السجل
- انسخ محتوى نافذة سطر الأوامر
- أرفق أي ملفات .log موجودة
- لقطة شاشة للخطأ

### 3. اختبارات إضافية
```bash
# معلومات النظام
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"

# معلومات .NET
dotnet --info

# اختبار بناء بسيط
dotnet new console -n TestApp
cd TestApp
dotnet run
```

## نصائح الأداء

### 1. لتحسين سرعة التطبيق
- أغلق البرامج غير الضرورية
- تأكد من وجود مساحة كافية على القرص الصلب
- استخدم SSD إذا أمكن

### 2. لتحسين استيراد البيانات
- قسم ملفات Excel الكبيرة إلى ملفات أصغر
- تأكد من تنسيق التواريخ الصحيح
- احذف الصفوف الفارغة من Excel

### 3. لتحسين البحث
- استخدم رقم التعريف الوطني للبحث السريع
- تجنب البحث بكلمات قصيرة جداً
- استخدم البحث المتقدم للنتائج الدقيقة
