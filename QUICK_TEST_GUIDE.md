# دليل التجربة السريع 🚀

## 🎯 خطوات تجربة التطبيق:

### 1. تشغيل التطبيق
```
انقر نقرة مزدوجة على: start-app.bat
```

### 2. اختبار الواجهة الرئيسية
- ستظهر نافذة التطبيق مع شريط الأدوات
- ستجد مربع البحث في الأعلى
- الجدول سيكون فارغاً في البداية

### 3. تجربة استيراد البيانات التجريبية
1. انقر على "استيراد Excel" في شريط الأدوات
2. اختر الملف: `sample_data.csv` (موجود في نفس المجلد)
3. انتظر رسالة نجاح الاستيراد
4. ستجد 5 موظفين تجريبيين في الجدول

### 4. تجربة البحث السريع
- اكتب في مربع البحث: "أحمد" أو "مهندس" أو "الجزائر"
- اضغط Enter أو انقر "بحث"
- ستجد النتائج مفلترة

### 5. تجربة البحث المتقدم
- انقر "بحث متقدم"
- جرب فلاتر مختلفة (الرتبة، الولاية، العمر، إلخ)
- انقر "بحث" لرؤية النتائج

### 6. تجربة طباعة البطاقة
- اختر موظف من الجدول (انقر على الصف)
- انقر "طباعة البطاقة"
- ستفتح معاينة البطاقة مع QR Code
- يمكنك طباعتها أو إغلاق المعاينة

### 7. تجربة التصدير
- انقر "تصدير" في شريط الأدوات
- اختر مكان حفظ الملف
- سيتم تصدير البيانات المعروضة حالياً

## 📊 البيانات التجريبية المتضمنة:
1. **أحمد محمد علي** - مهندس رئيسي - الجزائر
2. **فاطمة زهرة بن علي** - مفتش - وهران  
3. **محمد حسين الطاهر** - تقني سامي - قسنطينة
4. **سعاد الزهرة مرادي** - أستاذ مساعد - سطيف
5. **عمر بن عبد الله** - كاتب رئيسي - تلمسان

## ✨ المميزات المتوفرة:
- **العمر**: محسوب تلقائياً من تاريخ الميلاد
- **أقدمية المهنة**: محسوبة من تاريخ التوظيف
- **أقدمية الرتبة**: محسوبة من تاريخ التعيين
- **QR Code**: في بطاقة كل موظف
- **بحث ذكي**: في الاسم، الرقم، الرتبة، الولاية، مكان العمل

## 🔧 في حالة وجود مشاكل:
1. تأكد من تثبيت .NET 8.0 أو أحدث
2. تأكد من أن ملف `sample_data.csv` موجود
3. جرب إعادة تشغيل التطبيق
4. اتبع رسائل الخطأ إن وجدت

---
**ملاحظة**: هذا نظام متكامل يدعم 46 عمود من البيانات وأكثر من 2000 موظف! 🎉