@echo off
chcp 65001 >nul

REM التحقق من وجود .NET 8
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo  ❌ خطأ: .NET 8 غير مثبت           
    echo ========================================
    echo.
    echo 🚫 البرنامج يحتاج .NET 8.0 للعمل
    echo 📥 يرجى تثبيت .NET 8 أولاً
    echo.
    echo 🔧 للتثبيت: انقر نقرة مزدوجة على install-dotnet.bat
    echo 📖 أو اقرأ ملف: تعليمات_التشغيل.md
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  🎨 تشغيل النسخة العربية - خط Cairo
echo ========================================
echo.

echo ✨ المميزات العربية الجديدة:
echo • 🎨 خط Cairo الأنيق المُصمم للعربية
echo • 🔄 واجهة عربية كاملة RTL
echo • 📝 محاذاة يمين لجميع النصوص
echo • 🎯 ترتيب عربي للقوائم والأزرار
echo • 📊 جداول عربية التوجه
echo • 🔍 بحث عربي من اليمين لليسار
echo.

echo ✅ .NET 8 موجود - جاري التشغيل...
echo.

echo 🔨 بناء النسخة أولاً...
dotnet build -c Release --verbosity quiet

if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في البناء
    echo 🔧 جرب restart-professional.bat
    pause
    exit /b 1
)

echo ✅ تم البناء بنجاح
echo.

echo 🚀 تشغيل البرنامج...
echo.

REM تشغيل البرنامج
start "نظام إدارة الموظفين" "bin\Release\net8.0-windows\EmployeeManagementSystem.exe"

if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في التشغيل
    echo 🔍 تحقق من وجود ملف البرنامج
    pause
    exit /b 1
)

echo.
echo 🎊 تم تشغيل البرنامج بنجاح!
echo.
echo 📖 للمساعدة: اقرأ دليل_التشغيل_السريع.md
echo.
timeout /t 3 >nul